"""
Users Module - Portfolio Project

This module defines User and Owner classes for the portfolio To-Do application.
It demonstrates advanced OOP concepts including:
- Class inheritance and hierarchy
- Professional string representation methods
- Type hints and comprehensive documentation
- User role management and differentiation

Classes:
- User: Base class representing a general user
- Owner: Specialized class inheriting from User with ownership privileges

Author: [<PERSON>]
"""


# IMPORTS


import datetime  # For date/time operations if needed


# USER CLASS DEFINITIONS


class User:
    """
    Base User class representing a general user of the To-Do application.
    
    This class demonstrates:
    - Basic user attributes and functionality
    - Professional string representation
    - Type hints and comprehensive documentation
    - Foundation for inheritance hierarchy
    
    Attributes:
        name (str): The user's full name (title case)
        email (str): The user's email address
        date_joined (datetime.datetime): When the user joined (auto-generated)
    """
    
    def __init__(self, name: str, email: str) -> None:
        """
        Initialize a new User instance.
        
        Args:
            name (str): The user's full name
            email (str): The user's email address
            
        Returns:
            None: Constructors don't return values
            
        Example:
            >>> user = User("john doe", "<EMAIL>")
            >>> print(user.name)  # Output: "<PERSON>"
        """
        self.name = name.title()  # Convert to title case for consistency
        self.email = email.lower()  # Convert to lowercase for consistency
        self.date_joined = datetime.datetime.now()  # Auto-generate join timestamp
    
    def __str__(self) -> str:
        """
        Return string representation of the User.
        
        Returns:
            str: Formatted string showing user type and attributes
            
        Example:
            >>> print(user)
            User: <PERSON>e (<EMAIL>) - Joined: 2024-01-15 10:30:45
        """
        return f"User: {self.name} ({self.email}) - Joined: {self.date_joined.strftime('%Y-%m-%d %H:%M:%S')}"


class Owner(User):
    """
    Owner class representing a user with ownership privileges.
    
    This class demonstrates:
    - Inheritance from User base class
    - Extended functionality for ownership
    - Polymorphic string representation
    - Professional class hierarchy design
    
    Inherits all User attributes and adds:
        permissions (list[str]): List of owner permissions
        tasks_created (int): Number of task lists created by this owner
    """
    
    def __init__(self, name: str, email: str) -> None:
        """
        Initialize a new Owner instance.
        
        Args:
            name (str): The owner's full name
            email (str): The owner's email address
            
        Returns:
            None: Constructors don't return values
            
        Example:
            >>> owner = Owner("moses gana", "<EMAIL>")
            >>> print(owner.name)  # Output: "Moses Gana"
        """
        super().__init__(name, email)  # Initialize parent User class
        self.permissions = ["create_tasks", "edit_tasks", "delete_tasks", "manage_users"]  # List of permissions
        self.tasks_created = 0  # Track number of task lists created
    
    def __str__(self) -> str:
        """
        Return string representation of the Owner (polymorphic override).
        
        This method overrides the parent's __str__ method to provide
        Owner-specific information, demonstrating polymorphism.
        
        Returns:
            str: Formatted string showing owner type and attributes
            
        Example:
            >>> print(owner)
            Owner: Moses Gana (<EMAIL>) - Joined: 2024-01-15 10:30:45 - Task Lists: 3
        """
        base_info = f"Owner: {self.name} ({self.email})"
        join_info = f"Joined: {self.date_joined.strftime('%Y-%m-%d %H:%M:%S')}"
        task_info = f"Task Lists: {self.tasks_created}"
        return f"{base_info} - {join_info} - {task_info}"
    
    def create_task_list(self) -> None:
        """
        Increment the task list counter when owner creates a new task list.
        
        This method demonstrates owner-specific functionality.
        
        Returns:
            None: Method modifies instance state but doesn't return a value
        """
        self.tasks_created += 1
        print(f"Task list created. Total task lists: {self.tasks_created}")
