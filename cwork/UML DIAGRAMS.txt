UML DIAGRAMS FOR WEEK 4 

Diagram 1

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    - description: str
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
    + change_description(description: str)
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
    + view_overdue_tasks()
}

TaskList --> Task

@enduml

Diagram 2

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
}

TaskList --> Task

@enduml



Diagram 3

class Task {
    - title: str
    - completed: bool
    + mark_completed()
    + change_title(new_title: str)
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
}

TaskList --> Task

@enduml

Diagram 4

class Task {
    - name: str
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
}

TaskList --> Task

@enduml


UML DIAGRAM FOR WEEK 6

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    - description: str
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
    + change_description(description: str)
}

class RecurringTask {
    - interval: timedelta
    - completed_dates: list[datetime]
    + _compute_next_due_date(): datetime
    + mark_completed()
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    - uncompleted_tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + get_task(ix: int): Task
    + view_tasks()
    + view_overdue_tasks()
}

class TaskTestDAO {
    - storage_path: str
    + save_all_tasks(tasks: list[Task])
    + get_all_tasks(): list[Task]
}

class TaskCsvDAO {
    - storage_path: str
    + save_all_tasks(tasks: list[Task])
    + get_all_tasks(): list[Task]
}

class main

RecurringTask --|> Task
TaskList --> Task
TaskTestDAO --> Task
TaskCsvDAO --> Task
main --> TaskList
main --> TaskCsvDAO
main --> TaskTestDAO

@enduml

UML DIAGRAM FOR WEEK 7

@startuml Full_ToDoApp_Design

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    - description: str
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
    + change_description(description: str)
}

class RecurringTask {
    - interval: timedelta
    - completed_dates: list[datetime]
    + _compute_next_due_date(): datetime
    + mark_completed()
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    - uncompleted_tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + get_task(ix: int): Task
    + view_tasks()
    + view_overdue_tasks()
    + check_task_index(ix: int)
}

class TaskManagerController {
    - owner: str
    + add_task(task: Task)
    + complete_task(ix: int)
    + get_all_tasks(): list[list[int, Task]]
    + get_uncompleted_tasks(): list[Task]
    + load_tasks(path: str)
    + remove_task(ix: int)
    + save_tasks(path: str)
}

class CommandLineUI {
    - controller: TaskManagerController
    + run()
    + print_menu()
}

class TaskCsvDAO {
    - storage_path: str
    + save_all_tasks(tasks: list[Task])
    + get_all_tasks(): list[Task]
}

class TaskFactory {
    + create_task(title: str, date: datetime, **kwargs): Task
}

RecurringTask --|> Task
TaskList --> Task
TaskManagerController --> TaskList
CommandLineUI --> TaskManagerController
TaskManagerController --> TaskCsvDAO
TaskFactory ..> Task : creates
TaskFactory ..> RecurringTask : creates

@enduml



