UML DIAGRAMS FOR WEEK 4

Week 4 UML Class Diagram - Basic Task and TaskList Structure
@startuml Week4_Basic_Classes

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    - description: str
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
    + change_description(description: str)
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
    + view_overdue_tasks()
}

TaskList --> Task

@enduml

Diagram 2

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
}

TaskList --> Task

@enduml



Diagram 3

class Task {
    - title: str
    - completed: bool
    + mark_completed()
    + change_title(new_title: str)
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
}

TaskList --> Task

@enduml

Diagram 4

class Task {
    - name: str
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + view_tasks()
    + list_options()
}

TaskList --> Task

@enduml

Week 5 UML Class Diagram - Inheritance and Polymorphism
@startuml Week5_Inheritance_Polymorphism

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    - description: str
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
    + change_description(description: str)
}

class RecurringTask {
    - interval: timedelta
    - completed_dates: list[datetime]
    + _compute_next_due_date(): datetime
    + mark_completed()
}

class TaskList {
    - owner: Owner
    - tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + get_task(ix: int): Task
    + view_tasks()
    + view_overdue_tasks()
    + uncompleted_tasks: List[Task]
}

class User {
    - name: str
    - email: str
    - date_joined: datetime
    + __str__(): str
}

class Owner {
    + __str__(): str
}

RecurringTask --|> Task
Owner --|> User
TaskList --> Task
TaskList --> Owner

@enduml

Week 6 UML Class Diagram - DAO Pattern and Data Persistence
@startuml Week6_DAO_Pattern

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    - description: str
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
    + change_description(description: str)
}

class RecurringTask {
    - interval: timedelta
    - completed_dates: list[datetime]
    + _compute_next_due_date(): datetime
    + mark_completed()
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    - uncompleted_tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + get_task(ix: int): Task
    + view_tasks()
    + view_overdue_tasks()
}

class TaskTestDAO {
    - storage_path: str
    + save_all_tasks(tasks: list[Task])
    + get_all_tasks(): list[Task]
}

class TaskCsvDAO {
    - storage_path: str
    + save_all_tasks(tasks: list[Task])
    + get_all_tasks(): list[Task]
}

class main

RecurringTask --|> Task
TaskList --> Task
TaskTestDAO --> Task
TaskCsvDAO --> Task
main --> TaskList
main --> TaskCsvDAO
main --> TaskTestDAO

@enduml

UML DIAGRAM FOR WEEK 7

@startuml Full_ToDoApp_Design

class Task {
    - title: str
    - completed: bool
    - date_created: datetime
    - date_due: datetime
    - description: str
    + mark_completed()
    + change_title(new_title: str)
    + change_date_due(date_due: str)
    + change_description(description: str)
}

class RecurringTask {
    - interval: timedelta
    - completed_dates: list[datetime]
    + _compute_next_due_date(): datetime
    + mark_completed()
}

class TaskList {
    - owner: str
    - tasks: List[Task]
    - uncompleted_tasks: List[Task]
    + add_task(task: Task)
    + remove_task(ix: int)
    + get_task(ix: int): Task
    + view_tasks()
    + view_overdue_tasks()
    + check_task_index(ix: int)
}

class TaskManagerController {
    - owner: str
    + add_task(task: Task)
    + complete_task(ix: int)
    + get_all_tasks(): list[list[int, Task]]
    + get_uncompleted_tasks(): list[Task]
    + load_tasks(path: str)
    + remove_task(ix: int)
    + save_tasks(path: str)
}

class CommandLineUI {
    - controller: TaskManagerController
    + run()
    + print_menu()
}

class TaskCsvDAO {
    - storage_path: str
    + save_all_tasks(tasks: list[Task])
    + get_all_tasks(): list[Task]
}

class TaskFactory {
    + create_task(title: str, date: datetime, **kwargs): Task
}

RecurringTask --|> Task
TaskList --> Task
TaskManagerController --> TaskList
CommandLineUI --> TaskManagerController
TaskManagerController --> TaskCsvDAO
TaskFactory ..> Task : creates
TaskFactory ..> RecurringTask : creates

@enduml

Week 8 UML Class Diagram - Abstract Classes and Interface Design
@startuml Week8_Abstract_Classes

abstract class AbstractTask {
    - title: str
    - date_due: datetime
    - completed: bool
    - date_created: datetime
    + {abstract} mark_as_completed()
    + {abstract} get_task_type(): str
    + change_title(new_title: str)
    + is_overdue(): bool
}

abstract class Dice {
    - face: int
    + {abstract} roll(): int
    + get_face(): int
}

class SixSidedDice {
    + roll(): int
}

class TenSidedDice {
    + roll(): int
}

abstract class AbstractDAO {
    - storage_path: str
    + {abstract} get_all_tasks(): List[AbstractTask]
    + {abstract} save_all_tasks(tasks: List[AbstractTask])
}

class TaskTestDAO {
    + get_all_tasks(): List[AbstractTask]
    + save_all_tasks(tasks: List[AbstractTask])
}

SixSidedDice --|> Dice
TenSidedDice --|> Dice
TaskTestDAO --|> AbstractDAO

@enduml

Portfolio UML Class Diagram - Complete System Architecture
@startuml Portfolio_Complete_Architecture

abstract class AbstractTask {
    - title: str
    - date_due: datetime
    - completed: bool
    - date_created: datetime
    - description: str
    + {abstract} mark_as_completed()
    + {abstract} get_task_type(): str
    + change_title(new_title: str)
    + change_date(new_date: datetime)
    + is_overdue(): bool
}

class Task {
    + mark_as_completed()
    + get_task_type(): str
}

class RecurringTask {
    - interval: timedelta
    - completed_dates: List[datetime]
    + mark_as_completed()
    + get_task_type(): str
    + _compute_next_due_date(): datetime
}

class PriorityTask {
    - priority_level: int
    - PRIORITY_MAPPING: Dict[int, str]
    + mark_as_completed()
    + get_task_type(): str
    + get_priority_string(): str
}

class User {
    - name: str
    - email: str
    - date_joined: datetime
    + __str__(): str
}

class Owner {
    - task_lists_count: int
    + __str__(): str
}

abstract class AbstractDAO {
    - storage_path: str
    + {abstract} get_all_tasks(): List[AbstractTask]
    + {abstract} save_all_tasks(tasks: List[AbstractTask])
}

class TaskTestDAO {
    + get_all_tasks(): List[AbstractTask]
    + save_all_tasks(tasks: List[AbstractTask])
}

class TaskCsvDAO {
    + get_all_tasks(): List[AbstractTask]
    + save_all_tasks(tasks: List[AbstractTask])
}

class TaskFactory {
    + {static} create_task(title: str, date: datetime, **kwargs): AbstractTask
}

class TaskManagerController {
    - owner: str
    - task_list: TaskList
    - dao: AbstractDAO
    + add_task(task: AbstractTask)
    + complete_task(index: int)
    + get_all_tasks(): List[AbstractTask]
    + load_tasks()
    + save_tasks()
}

class CommandLineUI {
    - controller: TaskManagerController
    + run()
    + print_menu()
}

Task --|> AbstractTask
RecurringTask --|> AbstractTask
PriorityTask --|> AbstractTask
Owner --|> User
TaskTestDAO --|> AbstractDAO
TaskCsvDAO --|> AbstractDAO
TaskManagerController --> AbstractDAO
TaskManagerController --> TaskList
CommandLineUI --> TaskManagerController
TaskFactory ..> AbstractTask : creates

@enduml