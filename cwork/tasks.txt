
2
Practical Lab Exercises - Week 2 
 
 
COMP11124 Object Oriented Programming  1 
COMP11124 Object Oriented Programming  
Introduction to Python Programming II 
 
 
Learning Outcomes 
The practical lab exercises in this document continue the introduction to programming using the Python 
programming  language. After completing this week’s class,  you  will  be  able  to  build  more  complex 
programs that include conditionals, loops, and lists and can handle user input.  
 
Topics Covered 
Comparisons and Conditionals, Lists, Loops, Input,  
 
 
Getting Started Task: To retain all the code that you write and be able to work through the exercises in 
this lab, create a new Python file called: lab_week_2.py like how we did it last week. You can include 
the code of this week here and should continuously execute it to see the output.  If you want to see the 
value of a variable, please print it using the print() function, as I have omitted that in the sample code. 
1.  Comparisons and Conditionals In Python, comparison operators are used to compare values, and conditionals help control the flow of a 
program based on these comparisons.  
 
Exercise 1: Comparison Operators  
 
Comparisons in Python simply compare two or more values. A comparison always returns a Boolean value, 
meaning it can either be True or False.  
 
For example, consider the following code, which is not a comparison but stores a Boolean. 
 is_true = True 
Here we create a new variable and assign it the value:  True (Note: this is without quotes as it is not a 
String but a Boolean). You could also assign it the value False. 
 
A comparison in Python is very similar to us evaluating a statement such as: Is 5 larger than 4? The answer 
would be yes (which is equivalent to True) for our program. We can then turn this comparison into code 
and evaluate the output as follows: 
is_true = 5 > 4 
Where we are simply instructing the Python interpreter to store the output of the comparison whether 5 
is larger than 4. This will return True. 
 
Next  to  checking  whether  values  are  larger  and  smaller  using  the  following  operators:  <  (less  than),  > 
(greater than) we can also create a comparison using the following: 
- == (equal) 
- != (not equal 
- <= (less than or equal to) 
- >= (greater than or equal to) 
 
Practical Lab Exercises - Week 2 
 
 
COMP11124 Object Oriented Programming  2 
Task:  Follow  this  link  and  click  on  the  Try  It  button  for  each  of  the  comparison  operators.  Change  the 
values for each x variable to make the comparisons false.  
After this, you should be able to build your comparisons successfully based on variables.  
 
 
Exercise 2: Logical Operators 
 
Rather than using only a single comparison to check whether something is True or False, there are cases 
where you would want to combine them. For example, you want to check whether someone is between 
the ages of 20 and 30.  Using logical operators, you can combine those comparisons and evaluate them in 
one go. In our example we would therefore check: Is the age OVER 20 and UNDER 30. 
 
Python has three logical operators: 
- The and operator returns True if both operands are True. 
- The or operator returns True if at least one operand is True. 
- The not-operator returns True if the operand is False and vice versa. 
 
So for our example above we could use the and keyword to build our comparison: 
age = 25     
 
is_in_age_range = age > 20 and age < 30 
where age takes the value of the age that we want to check.  
 
Task: Follow this link and look through the examples in the Try It sections. 
 
Exercise 3: if – Conditionals 
 
Now that we have learned how we can make comparisons, we can start creating instructions that execute 
certain lines of code when a comparison is true.  
For example, in plain English, we can say that: If someone is over 18, they are an adult.  
We can then turn this into Python code using a simple if-conditional: 
age = 19 
age_group = "child" 
 
if age > 18: 
    age_group = "adult" 
 
print(f"The age group is {age_group}") 
 
In the first two lines, we assign an age and a default age_group. In the middle of the code lines, we have 
our if statement. It simply checks whether the age is over 18 and if it is (e.g. the comparison is True) it 
changes the age_group to adult in the indented code block. Indented code blocks (meaning the code is 
moved 4 spaces (or 1 tab) to the right) signal that they are related to the previous – non-indented code 
block.  
For your code, if you write indented code, use the tab symbol to indent the code.  
 
Task: Run this program twice, once with the given age and once when you change the age to something 
below 18. Observe the output and you will see that depending on the age, the age_group changes.  
 
 
Practical Lab Exercises - Week 2 
 
 
COMP11124 Object Oriented Programming  3 
Exercise 4: if – else Conditionals 
 
The  standard  if  conditional  only  allows  us  for  one  choice,  e.g.  if  the  comparison  is  True  do  something. 
However,  many  times  we  want  to  choose  one  option  out  of  two.  This  is  where  the  if-else  conditional 
comes in.  Let’s say we have a statement in plain English, and we want to check whether it is a windy day. 
“If the wind speed is over 10mph it is a windy day, otherwise it is a calm day”. Turning this statement into 
code would look as follows: 
wind_speed = 30 
 
if wind_speed < 10: 
    print("It is a calm day") 
else: 
    print("It is a windy day") 
 
First, we set an arbitrary wind speed, then in the last 4 lines we check whether the wind speed is less than 
10, and if this is true, we print that it is a calm day. 
The other option is indicated by the else statement and covers all other cases. For us, this means that 
when the wind speed is not smaller than 10, it is windy. 
Note that only one option will be executed, either the one following “if” or the one following “else”. 
 
Task: Run this program twice, once with the given wind speed and once  when you change the speed to 
something below 10. Observe the output and you will see that depending on the  wind speed, different 
parts will be executed.  
 
 
Exercise 5: if – elif - else Conditionals 
In some occasions, two choices following the if-else are not enough. There are cases where we have more 
than two options and we can also reflect this in Python code.  
Let’s say we are building a grading system and want to automatically give feedback based on a student’s 
mark. The following rules would apply: 
- 0-50% -> Fail 
- 50-60% -> Passed. 
- 60-70% -> Good Pass 
- 70-80% -> Excellent Pass 
We  can  reflect  this  in  Python by  putting  an  elif  block  in between the  if  and  else  blocks.  The elif 
(else-if) block behaves like an if conditional and simply checks whether another condition is true.  
grade = 55 
 
if grade < 50: 
    print("You failed") 
elif grade < 60: 
    print("You passed") 
elif grade < 70: 
    print("You got a good pass") 
else: 


Week 3

Learning Outcomes
The lab exercises in this class are the last part you need to start building your simple procedural Python programs. You will learn how to create and use functions as well as what variable scope is. Additionally, you will also investigate common errors in Python and how they are fixed.
At the end, you will build a simple To-Do application, which combines the things you have learned from the last three weeks.
 
It is essential that you finish the labs until next week as then, we will properly start looking at Object Oriented Programming, the main core topic of this module!
 
Topics Covered
Functions, Scope, Assertions, Common Python Errors
 
Getting Started Task: To retain all the code that you write and be able to work through the exercises in this lab, create a new Python file called: lab_week_3.py like how we did it last week. You can include the code of this week here and should continuously execute it to see the output. If you want to see the value of a variable, please print it using the print() function, as I have omitted that in the sample code.
1. Functions and Scope
 
Exercise 1: Functions in Python
 
A function is a block of code which only runs when it is called. You can pass data, known as parameters, into a function. A function can return data as a result.
Functions are very useful when you need to perform the same task multiple times throughout your code. For example, if we think of our temperature conversion example from the previous few weeks, we could turn this into a function. This would allow us to use the temperature conversion multiple times in our code without having to write the same code repeatedly. This is especially important if the size of our code is growing, and we want to keep it as clean and concise as possible.
 
Creating Functions
 
To create a function in Python, you use the def keyword, followed by the name of the function.
For example, let us create a function called greet_user that prints a greeting to the user. We can do this by using the following code:
 
def greet_user():
    print("Hello!")
 
Note that the function definition ends with a colon and the function name is followed by parentheses.
The code that the function executes is indented below the function definition. This is similar to how we indent code in if statements and loops.
If you want to call a function, you simply write the name of the function followed by parentheses. For example, to call the greet_user function, you would use the following code:
 
greet_user()
 
Function Parameters
 
Additionally, functions can take parameters, which are variables that are passed into the function when it is called. Before you can use a parameter in a function, you need to define it in the function definition. The parameters you use within a function definition need to be defined between the parentheses and ONLY exist within the function.
If we change our example to take a parameter called name, we can use the parameter in the function definition. We can do this by using the following code:
 
def greet_user(name):
    print(f"Hello {name}!")
 
Now, when we want to call the function, we need to pass a value (an argument) for the name parameter. We can do this as follows:
 
greet_user("John")
 
When working and reading about functions, you will often see the terms "parameter" and "argument". Although they both mean the same: data passed and used by the function, there is a difference between the two.
- A parameter is the variable listed inside the parentheses in the function definition and that is used within the function's body.
- An argument is the value that we pass to the function when we call it.
So, for our example above, name is a parameter and "John" is an argument.
 
If we now want to change it to have more than one parameter, we can do this by adding a comma between the parameters in the function definition. Let us use a user's first and last name as parameters.
 
def greet_user(first_name, last_name):
    print(f"Hello {first_name} {last_name}!")
 
When we call this new function, it is crucial that we pass the parameters in the correct order and that we pass the correct number of parameters.
 
greet_user("John", "Smith")
 
Keyword Arguments
 
Functions can not only have positional arguments but also keyword arguments. Keyword arguments are arguments that are passed to a function with a keyword and an equals sign. When we use keyword arguments, the order of the arguments does not matter. For example, we could call our greet_user function like this:
 
greet_user(last_name="Smith", first_name="John")
 
Note that we have switched the order, but the function still works.
 
Keyword arguments are useful when you have a function with many parameters, and you want to ensure that it is clear when calling the function which argument is which.
 
Default Values
 
Like the keyword arguments syntax, you can also set default values for parameters. This is very helpful when you have a function with many parameters and you want to set a default value for some of them. You need to define this default value in the function definition. For example, if we wanted to set a default value for an additional parameter called age, we could do this as follows:
 
def greet_user(first_name, last_name, university="UWS"):
    print(f"Hello {first_name} {last_name} from {university}!")
 
If we now call the function without passing a value for the university parameter, the function will use the default value that we have set in the function definition (UWS). For example, if we call the function like this, the output will be as follows:
greet_user("John", "Smith")
 
Output: Hello John Smith from UWS!
 
But if we include a value for the university parameter, the function will use the value that we have passed instead of the default value.
 
greet_user("John", "Smith", "UWS London")
# or
greet_user("John", "Smith", university="UWS London")
 
Output: Hello John Smith from UWS London!
Output: Hello John Smith from UWS London!
 
Both, using the positional arguments syntax and the keyword arguments syntax, are valid ways of calling a function. However, it is important to be consistent and use the same syntax throughout your code.
 
Task: Create a function called greet_friends. The function should take a list of names as a parameter and print a greeting for each name in the list. The greeting should be "Hello " followed by the name. For example, if the list contains the names "John", "Jane" and "Jack", the output should be as follows:
 
Hello John!
Hello Jane!
Hello Jack!
 
Note: You can use the for loop syntax to iterate over the list of names and use the following list as a starting point:
 
friend_list = ["John", "Jane", "Jack"]
 
# function code goes here
 
greet_friends(friend_list)
 
 
Return
 
Another important aspect of functions is that they can return values. This means that the function not only executes some code but returns a value that can be used in the rest of the code. We can for example store the returned value in a variable and then use it later in our code.
 
To return a value from a function, we use the return keyword followed by the value we want to return. For example, let us create a function that returns the sum of two numbers. We can do this by using the following code:
 
def add_numbers(num1, num2):
    return num1 + num2
 
 
We can also simply return a variable that we have defined in the function.
 
def add_numbers(num1, num2):
    result = num1 + num2
    return result
 
This is the same as the previous example, but we have assigned the result of the addition to a variable called result before returning it.
We can now use the function and store the returned value in a variable. For example, if we want to add the numbers 5 and 10, we can do this as follows:
 
result = add_numbers(5, 10)
print(result)
 
You may note that we have used the same variable name for the variable that stores the returned value as the variable that we have used in the function called result. This is not a problem, because the variable that we have defined in the function only exists within the function. The variable that we have defined outside of the function is a different variable and can be used without any problems. This is called variable scope and is an important concept in programming. We will look at this in more detail in the next exercise.
 
You can also return more than one value from a function. To do this, you simply need to separate the values you want to return with a comma. For example, if we want to return the sum and the product of two numbers, we can do this as follows:
 
def add_and_multiply_numbers(num1, num2):
    return num1 + num2, num1 * num2
 
or
 
def add_and_multiply_numbers(num1, num2):
    sum = num1 + num2
    product = num1 * num2
    return sum, product
 
(which is the same as the previous example, but may be easier to read if you are new to programming)
 
If you return more than one value from a function, you need to store the returned values in multiple variables. Simply separate the variables that will store the returned values with a comma.
 
sum, product = add_and_multiply_numbers(5, 10)
print(sum)
print(product)
 
Now that you understand how to create functions, let us do some exercises to practice what we have learned.
If you are unsure about how to solve the exercises, you can always look back at the previous examples or read through the following online guide (https://www.w3schools.com/python/python_functions.asp ) that covers the same topics as this exercise but in somewhat more detail.
 
Task Tax Calculation: 
1. Define a function called calculate_tax that takes two arguments: income and tax_rate. 
2. Inside the function, calculate the tax amount by multiplying income by tax_rate. 
3. Return the tax amount as the result. 
4. Call the calculate_tax function with an income of 50,000£ and a tax rate of 0.2 and print the calculated tax. 
5. Try using different incomes and tax rates in this function. 
Task Compound Interest Calculator Function: 
Your goal for this task is to write a function called compound_interest() that calculates the total amount of money earned by the investment every year. Here are the specifications: 
• The function should have three parameters: principal, duration and interest_rate.
• The function should print the total amount of money earned by the investment every year. 
• If the interest rate is smaller than 0 or larger than 1 the function should print out a message that says "Please enter a decimal number between 0 and 1" and return None. 
• If the duration is less than 0 the function should print out a message that says "Please enter a positive number of years" and return None. 
• The function should use a for loop to calculate the amount of money earned by the investment every year (Hint: use the range function and keep in mind that this starts at 0). The format of the output should be: "The total amount of money earned by the investment in year Y is X £" where X is the total amount of money earned by the investment and Y the year.
• The function should return the final investment value as an integer using the int() function 
The formula to calculate the total amount of money earned by the investment for each year is given by:  total_for_the_year = principal * (1 + interest_rate) ** year
 
You can test whether this function works by calling it like this: compound_interest(1000, 5, 0.03) and it should return £1159 for the result in year 5. 
 
 
Exercise 2: Variable Scope
 
Variable scope is an important concept in programming. It refers to the area of the code where a variable can be accessed.
A very simple example of variable scope is the following:
 
def new_function():
    my_new_variable = 5
 
new_function() # call the function. No problems here.
 
print(my_new_variable) # this will cause an error
 
We define a new variable called my_new_variable inside the function new_function. This variable only exists within the function and can only be accessed within the function. If we try to access the variable outside of the function, we will get an error. This is because the variable only exists within the function and not outside of it. This is called local scope.
Local scope only exists within functions but not within if statements or loops. For example, if we define a variable inside an if statement, we can still access it outside of the if statement. This is called global scope.
 
Why is this important? Well, you need to be aware of where you define your variables and need to remember that variables are defined within functions only! exist within the function.
On the other hand, variables defined outside of functions such as in the main body of the code can be accessed within functions. These variables exist within the global scope.
 
One thing you need to be also aware of is variables that have the same name but exist in different scopes.
 
my_new_variable = 0
 
def new_function():
    my_new_variable = 5
 
new_function()
 
print(my_new_variable)
 
It may be tempting to think that the variable my_new_variable in the function is the same as the variable my_new_variable outside of the function. If you run the code, you will see that this is not the case. The variable outside of the function is a global scope variable and the variable inside the function is a local scope variable. They are two different variables that just happen to have the same name.
 
Summarising this, be very careful when defining variables and make sure that you are aware of the scope of the variable. If you are unsure, you can always use a different variable name to avoid confusion.
 
 
2. Optional: Assertions and Errors
 
 
Exercise 3: Assertions 
Python assertions are used to validate that certain conditions hold during program execution. Assertions help you catch and handle errors early in your code. Generally, assertions are statements that check whether a given condition is true. If the condition is false, an AssertionError is raised, indicating a problem in the code. Assertions are useful for debugging and ensuring that your code works as expected and can stop problems from occurring before they become severe.  
If you built the function in exercise 5 correctly, the following code should run without problems: 
assert compound_interest(1000, 5, 0.03) == 1159 
More information on assertions can be found here: https://www.tutorialspoint.com/python/assertions_in_python.htm 
 
 
Exercise 4: Identifying and Fixing Common Errors 
Errors in Python tell you when something is wrong. There are two main kinds of errors which you will encounter when programming: Logical Errors, and Semantic Errors. 
Logical errors occur when the code is semantically correct, but you have built your program incorrectly. For example, you have a function that performs addition, but instead of using the + operator to add numbers together, you use the – one. 
Semantic errors are the kind of error that will lead to error messages either before, or during program execution. They are very common, and therefore you must know how to understand and fix them.  In this exercise, you will practice identifying and fixing common types of errors in Python code. These errors include Syntax, Name, Value, Index, and Indentation Errors. 
Below are examples of those errors in Python code. (Note: do not copy and paste the code as it will not work due to the errors.) 
Syntax Error: Occurs when code violates the rules of Python syntax. 
Example: We forgot to close the String quote. 
print("Hello World!) 
 
Name Error: This happens when a variable or name is not defined or misspelled. 
Example: We spell the variable we want to print incorrectly. 
my_name = "Alice" 
print("Hello, " + myname) 
Value Error: Occurs when a function receives an argument of the correct data type but an inappropriate value. 
Example: We want to turn a sequence of characters into an integer.  
my_string = "Hello World" 
print(int(my_string)) 
Index Error: This happens when trying to access an element of a sequence that is out of range. 
Example: We want to access the fourth element (index is 3) but the list only has three items. 
fruits = ["apple", "banana", "orange"] 
print(fruits[3]) 
Indentation Error: This occurs when the code's indentation is incorrect. 
Example: We have a conditional, but have not indented the code correctly.  
if 5 > 2: 
print("Five is greater than two!") 
 
Often enough, errors are highlighted in your code editor by either red or yellow squiggly lines as in the following example: 
 
Pay close attention to those lines to pick up errors and fix them quickly.  
 
Task - Fixing Errors: Copy and paste the following code snippets into your code one by one. Run each and then find and fix the error.  
• Fix the syntax error in the code below so it prints "Hello, World!" 
pritn("Hello, World!") 
Name Error: 
• Correct the name error by defining the missing variable to print "My favorite color is Blue." 
print("My favorite color is", favorite_color) 
Value Error: 
• Fix the value error by changing the string to an integer so the sum is correctly calculated and printed. 
number1 = "5" 
number2 = 3 
result = number1 + number2 
print("The sum is:", result) 
Index Error: 
• Correct the index error by accessing the second element (index 1) of the list and printing it. 
fruits = ["apple", "banana", "cherry"] 
print(fruits[3]) 
Indentation Error: 
• Fix the indentation error so the code correctly prints "Good morning!" when the time is before 12:00. 
time = 11 
if time < 12: 
print("Good morning!")  
 
 
3. Your first larger-scale Python programme 
In this exercise, you will create a simple to-do list program using Python. You will use variables, lists, input, loops, functions, and conditionals to build a basic but functional to-do list manager. 
Task To-Do list manager: 
You need to create a to-do list manager with the following functionalities: 
1. Initialize an empty list to store tasks. 
2. Implement a menu that allows the user to perform the following actions: 
• Add a new task to the list. 
• View the current tasks in the list. 
• Remove a task from the list. 
• Quit and exit the program. 
3. Use a while loop to repeatedly display the menu and handle user input. 
4. Create functions for adding, viewing, and removing tasks. 
5. Use conditionals to execute the appropriate function based on the user's choice. 
6. Display a message if the user tries to remove a task that doesn't exist. 
7. Exit the program when the user chooses to quit. 
Create a new file called to_do_week_3.py and use the following (incomplete) template to get started. 
When you work on the script it is beneficial to run it in intervals to see if the code you have implemented works. 
# Initialize an empty list to store tasks 
tasks = # TODO: Add code here to initialize an empty list 
 
# Function to add a task to the list 
def add_task(): 
    pass  # TODO: Add code here to add a task to the list 
    # Don't forget to add arguments to the function, if needed 
 
# Function to view current tasks in the list 
def view_tasks(): 
    pass  # TODO: Add code here to view all tasks in the list 
 
# Function to remove a task from the list 
def remove_task(): 
    pass  # TODO: Add code here to remove a task from the list 
 
# Main program loop 
while True: 
    print("To-Do List Manager") 
    print("1. Add a task") 
    print("2. View tasks") 
    print("3. Remove a task") 
    print("4. Quit") 
     
    choice = input("Enter your choice: ") 
     
    if choice == "1": 
        pass # TODO: Add code for this option here and replace the pass statement 
 
    # TODO: Add code for other menu options here 
 
    else: 
        print("Invalid choice. Please try again.") 
 
Hints: 
• You can (and should) add function arguments as necessary. 
• You can use the append() method to add tasks to the list. 
• To view tasks, you can loop through the list and print each task with a numerical index. 
• To remove a task, consider using the remove() method or handle it with a loop and conditional statements. 
 
 
1
 
COMP11124 Object Oriented Programming


1
Practical Lab Exercises - Week 4 v1.2 – 2023/2024 
 
 1 
COMP11124 Object Oriented Programming 
Classes and Objects 
 
 
Learning Outcomes 
In this lab, you will advance your understanding of Python and will learn how to create Python classes 
and how to use them in your code. You will also learn how to add (and use) methods and attributes to 
your classes as well as be able to discuss how and why one should modularize and document their code. 
 
Topics Covered 
Python: Classes, Methods, Objects, Modularizing code, documentation using docstrings and comments.  
 
 
Getting Started Task: To retain all the code that you write and be able to work through the exercises in 
this lab, create a new Python file called: lab_week_4.py like how we did it last week. You can include 
the code of this week here and should continuously execute it to see the output. If you want to see the 
value of a variable, please print it using the print() function, as I have omitted that in the sample code. 
1.  Python Classes  
Exercise 1: Creating Classes and Initializing Objects. 
 
The last task of last week’s class was to create a To Do List program using functions.  
This poses ideal to be turned into an OOP program.  
Remember, we had a "ToDo list manager" loop that checked for our input, and we could add, remove, 
and list tasks.  
Look at the following UML diagram to see an example of how this functionality could be implemented in 
an OOP program. 
  
First, we have a class called TaskList. The top part shows us the Names of the classes, followed by their 
attributes.  In  this  case, we  have  a  Python  list  called  tasks  that  will  store  all  our  tasks.  We also have a 
string called owner, which will store the name of the owner of the task list. 
 
This class handles the task-management functionality. It has four methods: add_task, remove_task, 
view_tasks and list_options: 
- The add_task method takes a parameter called task which should be a Task object. It then adds the 
task to the tasks list. 
Practical Lab Exercises - Week 4 v1.2 – 2023/2024 
 
 2 
- The remove_task method takes a parameter ix which is the index of the task that should be removed 
from the tasks list. 
- The view_tasks method prints all tasks in the tasks list. 
- The list_options method prints the options that the user must interact with the task list and handles 
the user input. 
 
We also have a class called Task. To keep it simple, we do not have added methods (yet). We simply have 
included a task title as an attribute. 
 
If you want to define a class in Python, you use the class keyword followed by the name of the class. In 
our case, this is as follows: 
 
class TaskList: 
 
 
We can now add the attributes to the class. To do this, we need to define them in the __init__ 
method. This method is always called when we create an object of the class. We can define the 
attributes in the __init__ method by using the following code: 
 
class TaskList: 
    def __init__(self, owner): 
        self.owner = owner 
        self.tasks = [] 
 
This then allows us to use the class and create new objects. 
 
my_task_list = TaskList("John") 
print(my_task_list.owner) 
 
Note that we have passed the owner parameter when creating the object. This is because we have 
defined the owner attribute in the __init__ method and therefore need to pass a value for it when 
creating it. If you leave out the owner parameter, you will get an error.  
Within the print statement, we have used the dot notation to access the owner attribute of the 
my_task_list object. This is because the owner attribute is an attribute of the my_task_list 
object. 
So, if you create a new object of the TaskList class, you can access the owner attribute of that object 
by using the dot notation. 
 
someone_else_task_list = TaskList("Jane") 
print(someone_else_task_list.owner) 
 
Within the __init__ method we can not only define attributes but also handle other logic such as 
calling methods. If your task list hypothetically wants to store the owner's name in uppercase, you could 
do this as follows: 
 
class TaskList: 
    def __init__(self, owner): 
        self.owner = owner.upper() 
        self.tasks = [] 
 
 



1
Practical Lab Exercises - Week 5 v1.2 – 2023/2024 
 
 1 
COMP11124 Object Oriented Programming 
Inheritance and Polymorphism 
 
 
Learning Outcomes 
In  this  lab,  you  will  learn  about  inheritance  and  polymorphism  in  Python.  You  will  learn  how  to  create 
subclasses and how to override methods.  We will also look at the super() function and how to use it 
to call the parent class methods. After you have completed this lab, you should be able to: 
- critically evaluate the use of inheritance and polymorphism 
- demonstrate the use of inheritance and polymorphism in Python 
- understand how we use inheritance and polymorphism in OOP 
 
Topics Covered 
Python: inheritance, polymorphism, super() function 
 
 
Getting Started Task: You should have completed the ToDoApp from last week. If you have not, please 
complete it now. You will need it for this week's lab. Copy and paste it (so that you have some indication 
of the progress you have made) and then rename the folder to ToDoAppWeek5. 
There are some exercises that you should use a new lab_week5.py file for. In others, you will use the 
ToDoAppWeek5 folder. The exercises will tell you where to use which file/folder. 
 
IMPORTANT NOTE: Last week we worked on the ToDo app and have included type hints and docstrings. 
The first part of this omits this in its examples for brevity. However, this does not mean that you should 
just  forget  about  type  hints  and  docstrings.  Try  to  change  any  code  that  you  add  to  any  of  the  files  to 
include type hints and docstrings. This will help you to get into the habit of adding them to your code. 
 
1. Inheritance  
Let us quickly recap the lecture content. Inheritance is a way to create new classes from existing classes; 
We are trying to model an "is-a" relationship. For example, a Plane is a Vehicle. We can therefore create 
a Plane class that inherits from the Vehicle class.  
 
In Python, every class technically inherits from the object class (although we do not need to specify this).  
There are several reasons why we would want to use inheritance.  
1) The major reason is that it allows us to reuse code. For each child class that we create, we do not 
need  to  redefine  the  methods  and  attributes  that  are  already  defined  in  the  parent  class.  This 
saves you from possibly rewriting the same code multiple times. 
2) It allows us to create a hierarchy of classes. This makes it easier to understand the code and allows 
us to create more complex programs. In the previous example we could have a hierarchy like this: 
          Vehicle 
    /            \ 
Car              Plane 
                /  \               /    \ 
Electric Petrol     Propeller  Jet  
 
Practical Lab Exercises - Week 5 v1.2 – 2023/2024 
 
 2 
All vehicles have certain attributes and methods that are shared, for example, colour, weight, max 
speed, etc. However, each child class may have additional attributes and methods that are specific 
to that child class. For example, a plane has a wingspan, but a car does not.  
 
3) It  enables  a  concept  called  overriding.  This  means  that  we  can  change  the  functionality  of  a 
method in a child class. For example, we may have a method called move in the Vehicle class. This 
method may simply print "The vehicle is moving". However, we want to be more specific for each 
child class. For example, for a car we may want to print "The car is driving" and for a plane, we 
may want to print "The plane is flying". In overriding we simply recreate the method in the child-
class and change the functionality. 
 
Ok, enough theory, let us get started with some exercises. We will start with a simple example and then 
move on to the ToDoApp. 
 
Exercise 1: Simple Inheritance 
 
For this part, use the lab_week5.py file. 
 
Let us assume the example from above. We are going to create this hierarchy.  
First,  we  need  to  create our base  Vehicle  class.  This  class will  contain  all  the  attributes  and  methods 
that are shared by all vehicles. 
 
class Vehicle: 
    def __init__(self, colour, weight, max_speed): 
        self.colour = colour 
        self.weight = weight 
        self.max_speed = max_speed 
 
    def move(self, speed): 
        print(f"The vehicle is moving at {speed} km/h") 
 
We  can  see  that  each  vehicle  has  a  colour,  weight  and  max_speed.  This  is  quite  simplistic,  and  we 
could add more attributes and methods. However, for the sake of this lab, we will stick with these three 
attributes and one method. 
 
Now let us create our cars. We need to create a class called  Car that inherits from the  Vehicle class. 
The syntax to inherit from a class is as follows: 
 
class NewClassName(ParentClassName): 
 
So, for the Car, we can do this as follows: 
 
class Car(Vehicle): 
 
    def move(self, speed): 
        print(f"The car is driving at {speed} km/h") 
 
Here, we have specified that the Car class inherits from the Vehicle class, which means all attributes 
will also be available in the  Car class. We have also overridden the  move method. This means that the 
move method in the Car class will be used instead of the move method in the Vehicle class. 
 

1
Practical Lab Exercises - Week 6 v1.0 – 2023/2024 
 
 
COMP11124  Object Oriented Programming – Dr Jacob Koenig 1 
COMP11124 Object Oriented Programming 
Debuggi ng, P ropert i es  and  P ers i s t ence 
 
 
Learning Outcomes 
In this  lab, you  will learn about  how  to  debug  your Python  code,  find out  the  usage of  Python  properties 
and  a  way  to  implement  persistence:  topics are  important  for  your  programming .  After  you  have 
completed this  lab, you should  be able to: 
- Understand  and apply  debugging techniques to  your  code. 
- Design and implement persistence, following OOP  principles. 
- Demonstrate the  use of the Python  property to handle managed attributes.  
 
Topics Covered 
Python:  Debugging, property in Python and persistence using the DAO pattern with  CSV and Pickle 
 
 
Getting  Started  Task:  Download  the  file  called  lab_week_6_debugging.py  from  Aula  and  open  it  in 
VSCode.  
 
1. Debugging  
The  debugging  process  is an important  part  of  programming. It allows  you  to  find  and  fix errors in  your 
code.  A debugger is a tool  that  allows you  to step  through  your code  and see  what  is happening  at each 
step.  This saves you from writing lots and lots of print statements  to see what is happening in your code. 
 
Read the first part of this link to understand some more of the key terminology: 
https://aws.amazon.com/what-is/debugging/   
 
In  this  exercise,  we  are going  to  use  the  debugger  in  VSCode  to  debug  a  simple  Python  program. This 
lab_week_6_debugging.py file contains a simple Python  program that has a bug (error) in it. This error 
is not  a syntax  error, but  a logical error.  The  program  runs,  but  it  does  not  produce  the  correct  output. 
Those errors are the most difficult to find and fix and a debugger can help you with this. 1 
  
 1The example has  been adapted from  https://www.jetbrains.com/help/pycharm/creating-and-running-your-firs t-python-
project.html   and  https://www.jetbrains.com/help/pycharm/debugging-your-firs t-python- application .html  to fit  the context 
of this  lab (e.g.  using  VSCode instead  of PyCharm). 
Practical Lab Exercises - Week 6 v1.0 – 2023/2024 
 
 
COMP11124  Object Oriented Programming – Dr Jacob Koenig 2 
Exercise  1: Finding the Problem 
 
First, read the  code within  the  file and try to  understand  it.  Then,  run the script, accelerate the car once, 
and then  brake it twice by typing the corresponding console  window.   
 
 
Now  press o followed by Enter to show the  car's odometer: 
 
 
The script tells us that the car has travelled 0 kilometres! It's an unexpected result because we've pushed 
the accelerator once, so the car should  have covered some distance. Let's debug  the code to  find out  the 
reason for that. 
 
To  start debugging,  you  have  to  set  a breakpoint  first. The  debugger will  stop  just  before  executing  the 
line with  the breakpoint,  and you  will be able to examine the  current state  of the program. The state will 
include the values of all variables, the call stack and more.  
To set a breakpoint, click on the part to the left of the line number where you want to set the breakpoint. 
A red dot will appear before the line number, indicating that a breakpoint is set. 
 
The car's odometer is set on  line 15, so let's put a breakpoint there. Click the area before the line number: 
  

1
Practical Lab Exercises - Week 7 v1.0 – 2023/2024 
 
 
COMP11124  Object Oriented Programming – Dr Jacob Koenig 1 
COMP11124 Object Oriented Programming 
S OLID and P y t hon Except i ons  
 
 
Learning Outcomes 
After you have completed  this lab, you should  be able to: 
- Discuss the fundamental principles of SOLID (Single Responsibility, Open/Closed, Liskov 
Substitution, Interface  Segregation,  Dependency  Inversion)  and  their  significance  in  software 
design. 
- Critically appraise  code  examples  to  identify  violations  of  SOLID  principles  and  propose code 
refactoring to adhere to them. 
- Demonstrate proficiency in handling exceptions in Python, including raising  and catching 
exceptions. 
- Understand  the  separation of concerns principle and how to  apply it. 
 
Topics Covered  
Python:  SOLID Principles, Python Exceptions,  Separation of Concerns in the ToDO App.   
 
 
Getting Started  Task:  Create  a  new  copy  of  your  ToDo  app  for  this  week  (e.g.  suffix  it  with  _week7  or 
similar).  
 
1. SOLID Principles 
 
As discussed  in the lecture, there are several OOP principles that, if followed, can help you to write 
better OO code. A commonly used set of principles follows the SOLID acronym. These principles are:  
    S: Single Responsibility Principle. Ensures classes have a single responsibility, enhancing modularity.   
    O: Open/Closed Principle. Allows for extension of classes without modifying existing code.   
    L: Liskov Substitution  Principle. Subclasses can substitute  superclasses without  altering functionality.   
    I:  Interface Segregation Principle. Clients only depend on the interfaces they use, promoting 
modularity. 
    D: Dependency Inversion Principle. High-level modules depend on abstractions, not  low-level details. 
 
Let us now  look at each in detail, see some code examples and then apply them to our ToDoApp.  
We will be making use of code that is hosted on GitHub.  If you have not  heard of GitHub, it is a platform 
that allows you  to store  and share your code with others.  For software developers,  it is a very good 
resource as it allows you to: see code examples,  collaborate with others, and store your code  in a safe 
place.  
 
Exercise  1. Single Responsibility Principle 
 
The single responsibility principle states that a class should have only one reason to  change.  Essentially, 
this means that a class should only  have one job. If a class has more than one job, it is more difficult to 
maintain and extend and violates the SRP.  
Practical Lab Exercises - Week 7 v1.0 – 2023/2024 
 
 
COMP11124  Object Oriented Programming – Dr Jacob Koenig 2 
A term that we will often hear in this regard is "separation of concerns". This is a design principle for 
separating a computer program into distinct sections, such that each section addresses a separate 
concern. A concern is a set of information that affects the code of a computer program. 
Additionally,  we also want to avoid "coupling".  Coupling is the degree of interdependence between 
software modules;  a measure of how closely connected two  routines  or modules are;  and the strength 
of the relationships  between  modules. Good  OO design aims to reduce coupling as much as possible.  
 
For this principle, look at the following code example from GitHub: 
https://github.com/heykarimoff/solid.python/blob/master/1.srp.py  
 
Read the code and the explanations  and you  should  be able to understand  SRP within  Python.   
This code also mentions  the Facade Pattern. This is a design pattern that provides a simplified interface 
to a larger body of code, such as a class library. (Highly recommended reading here: 
https://refactoring.guru/design-patterns/facade  ). 
Another  example of SRP (with good and bad examples) can be found here: 
https://github.com/gicornachini/SOLID/tree/master/SRP   
 
Now,  think  of our ToDoApp.  We have the following classes (without  the portfolio  tasks included):  
- Task 
- RecurringTask 
- TaskList 
- TaskTestDAO 
- TaskCsvDAO 
- TaskPickleDAO (optional) 
 
If we look at each class, we can see that without directly thinking about the  SRP, each class has only a 
single responsibility. The Task/RecurringTask class is responsible for representing a task, the 
TaskList class is responsible for managing a list of tasks, and the DAO classes are responsible for 
reading and writing tasks to a file.  
One part where we are very likely violating the SRP is the main module. We use this (although it is not a 
class) to handle the user input,  print the menu and call methods of the various classes. This is a clear 
violation of the SRP. We should  aim to separate the user interface from the business logic. We will fix 
this later in the lab. 
 
Exercise  2. Open/Closed Principle 
 
The Open/Closed Principle states that functionality  should  be open  for extension but  closed for 
modification. This means that you should  be able to extend a class's  behaviour, rather than modify it if 
we want  to add a new feature. This is often achieved by using inheritance and polymorphism. You can 
find a set of Python  code examples that demonstrate  the OCP here: 
https://github.com/heykarimoff/solid.python/blob/master/2.ocp.py   
Another  example is here: https://github.com/gicornachini/SOLID/tree/master/OCP (but please ignore 
the ABCMeta, abstractmethod, as we will cover that next week). 
 
In very simple terms, to implement the OCP think about future changes that may be made to your code. 
If you think  that you  may need to add a new feature to a class, it would be best to create a higher -level 
class that the original class inherits from. This way, if you create a new subclass  that is similar, but has 
some additional functionality,  you can simply add the new functionality  to the  subclass (after inheriting) 
without  modifying the original class. 
 

1
Practical Lab Exercises - Week 8 
 
 
COMP11124 Object Oriented Programming  1 
COMP11124 Object Oriented Programming 
Data Structures and Abstract Classes 
 
 
Learning Outcomes 
After you have completed this lab, you should be able to: 
- Understand, select, and implement suitable data structures for various problems. 
- Evaluate the use of abstract classes and how they can enable OOP. 
 
Topics Covered  
Python: Tuples, Sets, Dicts, Nested Lists, Abstract Classes 
 
 
Getting Started Task: Create a file for this week’s class (e.g. lab_week_8.py or similar).  
 
Important Note:  You will  notice that this week’s exercises are shorter than in previous weeks. This has 
two  reasons:  1)  I  would  like  to  ensure  that  everyone  has  completed  all  the  content  from  the  previous 
week (e.g. built out their ToDo app) as in doing so, you will “learn-by-doing”. 2) I want to also ensure that 
there is ample time to work on any portfolio tasks.  Therefore, if you have not finished last weeks class, 
please work on this in this class.  
 
1.  Data Structures  
In the lecture, we covered data structures in Python. Previously we have looked at lists, so now let us 
look at tuples, sets and dictionaries. 
We will use external resources to learn about these data structures and this will be followed by a 
collection of smaller tasks that you can complete to practice using these data structures. 
 
Exercise 1: Tuples 
 
A tuple is a collection which is ordered and unchangeable. In programming, "unchangeable" is also 
called immutable. Immutability is very important as it allows us to write less error-prone code. If you 
know that a value should not be changed, you can use a tuple to store it. Tuples are written in round 
brackets similar to a list.  
 
Look (and work) through the exercises and descriptions here to get an overview of tuples: 
https://www.w3schools.com/python/python_tuples.asp  
 
Exercise 2: Sets 
 
A set is a collection which is unordered and unindexed. Unindexed means that you cannot access the 
elements of a set by referring to an index. Sets in Python are created either by taking a list and 
converting it to a set using the set() function or by using curly brackets.  
 
Look through the exercises and descriptions here to get an overview of sets: 
https://www.w3schools.com/python/python_sets.asp 
Practical Lab Exercises - Week 8 
 
 
COMP11124 Object Oriented Programming  2 
 
Exercise 3: Dictionaries 
 
A dictionary is a collection which is unordered, changeable, and indexed. In Python dictionaries are 
written with curly brackets, and they have keys and values. 
The two ways to create a dictionary are by using the curly brackets or by using the dict() function. 
my_dict = { 
  "brand": "Ford", 
  "model": "Mustang", 
  "year": 1964 
} 
 
or 
my_dict = dict(brand="Ford", model="Mustang", year=1964) 
 
Look and work through the exercises and descriptions here to get an overview of dictionaries: 
https://www.w3schools.com/python/python_dictionaries.asp  
 
Exercise 4: Using Data Structures 
 
Now that you have learned about tuples, sets and dictionaries, you can complete the following tasks to 
practice using them. 
 
Task 1: Tuples 
In the lecture, we looked at packing and unpacking tuples. In that regard, say we have two variables: a 
and b. We want to swap their values without using a temporary variable.  
a = 5 
b = 10 
Write a program that swaps the values of a and b using a tuple (or two). 
 
 
Task 2: Sets 
Given two sets of names: 
set1 = {"Tom", "Jerry", "Hewey", "Dewey", "Louie"} 
set2 = {"Tom", "Garfield", "Snoopy", "Hewey", "Dewey"} 
 
Write code that compares the two sets and only returns the names that are in both sets. 
 
Task 3: Dictionaries 
Create a function called histogram that takes a list as a parameter and returns a dictionary where the 
keys are the elements of the list and the values are the number of times the element appears in the list. 
For example, if the list is [1, 2, 3, 1, 2, 3, 4], the function should return {1: 2, 2: 2, 3: 2, 4: 1}. 
You can use the following list to test your function: 
my_list = [1, 2, 3, 1, 2, 3, 4] 
assert histogram(my_list) == {1: 2, 2: 2, 3: 2, 4: 1} 
 
 
 
 
