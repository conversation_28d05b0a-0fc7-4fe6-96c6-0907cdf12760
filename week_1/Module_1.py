# Module 1 Lab

# Variable Definition

my_int = 5
my_float = 5.5
my_string = "Hello World"
my_bool = True

print(my_int)
print(my_float)
print(my_string)
print(my_bool)

my_int_float = float(my_int)
my_float_int = int(my_float)
my_bool_int = int(my_string)

print(my_int_float)
print(my_float_int)
print(my_bool_int)

num_1 = 5 
num_2 = 4

#Addition
result_addition = num_1 + num_2

#Subtraction
result_subtraction = num_1 - num_2

#Multiplication
result_multiplication = num_1 * num_2

#Division
result_division = num_1 / num_2

#Floor Division
result_floor_division = num_1 // num_2

#Modulus
result_modulus = num_1 % num_2

#Exponentiation
result_exponentiation = num_1 ** num_2



print(num_1)
print(num_2)
print(result_average)
