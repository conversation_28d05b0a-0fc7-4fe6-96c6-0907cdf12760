{".class": "MypyFile", "_fullname": "_ast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.AST", "name": "AST", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.AST", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.AST.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "_ast.AST._attributes", "name": "_attributes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "_ast.AST._fields", "name": "_fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Add", "name": "Add", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Add", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Add", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "And": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.boolop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.And", "name": "And", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.And", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.And", "_ast.boolop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnnAssign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON>", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.<PERSON><PERSON><PERSON>.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}, "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}, "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}, "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}, "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}, "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}, "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "target"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "annotation"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "simple"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AnnAssign.annotation", "name": "annotation", "type": "_ast.expr"}}, "simple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AnnAssign.simple", "name": "simple", "type": "builtins.int"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AnnAssign.target", "name": "target", "type": {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AnnAssign.value", "name": "value", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Assert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON><PERSON>", "name": "Assert", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON><PERSON>", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "test", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Assert.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "test", "msg", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON><PERSON>", "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Assert", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Assert.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "test"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "msg"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Assert.msg", "name": "msg", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Assert.test", "name": "test", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Assign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Assign", "name": "Assign", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Assign", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Assign", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "targets", "value", "type_comment", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Assign.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "targets", "value", "type_comment", "kwargs"], "arg_types": ["_ast.Assign", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "_ast.expr", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Assign", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Assign.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "targets"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Assign.targets", "name": "targets", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Assign.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Assign.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.AsyncFor", "name": "AsyncFor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.AsyncFor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.AsyncFor", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.AsyncFor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "arg_types": ["_ast.AsyncFor", "_ast.expr", "_ast.expr", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.AsyncFor.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "target"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "iter"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "orelse"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFor.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFor.iter", "name": "iter", "type": "_ast.expr"}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFor.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFor.target", "name": "target", "type": "_ast.expr"}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFor.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFunctionDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.AsyncFunctionDef", "name": "AsyncFunctionDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.AsyncFunctionDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.AsyncFunctionDef", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.AsyncFunctionDef.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.AsyncFunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.AsyncFunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.AsyncFunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.AsyncFunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.AsyncFunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.AsyncFunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.AsyncFunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.AsyncFunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.AsyncFunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.AsyncFunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.AsyncFunctionDef.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "args"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "decorator_list"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "returns"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_params"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFunctionDef.args", "name": "args", "type": "_ast.arguments"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFunctionDef.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "decorator_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFunctionDef.decorator_list", "name": "decorator_list", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFunctionDef.name", "name": "name", "type": "builtins.str"}}, "returns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFunctionDef.returns", "name": "returns", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFunctionDef.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncFunctionDef.type_params", "name": "type_params", "type": {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncWith": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.AsyncWith", "name": "AsyncWith", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.AsyncWith", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.AsyncWith", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.AsyncWith.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "arg_types": ["_ast.AsyncWith", {".class": "Instance", "args": ["_ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncWith", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.AsyncWith.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "items"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncWith.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncWith.items", "name": "items", "type": {".class": "Instance", "args": ["_ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AsyncWith.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Attribute", "name": "Attribute", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Attribute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Attribute", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "attr", "ctx", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Attribute.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "attr", "ctx", "kwargs"], "arg_types": ["_ast.Attribute", "_ast.expr", "builtins.str", "_ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Attribute", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Attribute.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "attr"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ctx"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Attribute.attr", "name": "attr", "type": "builtins.str"}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Attribute.ctx", "name": "ctx", "type": "_ast.expr_context"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Attribute.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AugAssign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.AugAssign", "name": "AugAssign", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.AugAssign", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.AugAssign", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "target", "op", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.AugAssign.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "target", "op", "value", "kwargs"], "arg_types": ["_ast.AugAssign", {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}, "_ast.operator", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AugAssign", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.AugAssign.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "target"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "op"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AugAssign.op", "name": "op", "type": "_ast.operator"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AugAssign.target", "name": "target", "type": {".class": "UnionType", "items": ["_ast.Name", "_ast.Attribute", "_ast.Subscript"], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.AugAssign.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Await": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Await", "name": "Await", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Await", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Await", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Await.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["_ast.Await", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Await", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Await.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Await.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BinOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.BinOp", "name": "BinOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.BinOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.BinOp", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "op", "right", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.BinOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "op", "right", "kwargs"], "arg_types": ["_ast.BinOp", "_ast.expr", "_ast.operator", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BinOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.BinOp.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "op"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.BinOp.left", "name": "left", "type": "_ast.expr"}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.BinOp.op", "name": "op", "type": "_ast.operator"}}, "right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.BinOp.right", "name": "right", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitAnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.BitAnd", "name": "BitAnd", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.BitAnd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.BitAnd", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitOr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.BitOr", "name": "BitOr", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.BitOr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.BitOr", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitXor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.BitXor", "name": "BitXor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.BitXor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.BitXor", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BoolOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON>ol<PERSON>p", "name": "BoolOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON>ol<PERSON>p", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON>ol<PERSON>p", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "values", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Bool<PERSON>p.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "values", "kwargs"], "arg_types": ["_ast.<PERSON>ol<PERSON>p", "_ast.boolop", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoolOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "op"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.BoolOp.op", "name": "op", "type": "_ast.boolop"}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.BoolOp.values", "name": "values", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Break": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Break", "name": "Break", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Break", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Break", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Call", "name": "Call", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Call", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Call", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "func", "args", "keywords", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Call.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "func", "args", "keywords", "kwargs"], "arg_types": ["_ast.Call", "_ast.expr", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Call", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Call.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "func"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "args"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "keywords"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Call.args", "name": "args", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Call.func", "name": "func", "type": "_ast.expr"}}, "keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Call.keywords", "name": "keywords", "type": {".class": "Instance", "args": ["_ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.ClassDef", "name": "ClassDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.ClassDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.ClassDef", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "bases", "keywords", "body", "decorator_list", "type_params", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.ClassDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "bases", "keywords", "body", "decorator_list", "type_params", "kwargs"], "arg_types": ["_ast.ClassDef", "builtins.str", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClassDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.ClassDef.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bases"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "keywords"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "decorator_list"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_params"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "bases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ClassDef.bases", "name": "bases", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ClassDef.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "decorator_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ClassDef.decorator_list", "name": "decorator_list", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ClassDef.keywords", "name": "keywords", "type": {".class": "Instance", "args": ["_ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ClassDef.name", "name": "name", "type": "builtins.str"}}, "type_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ClassDef.type_params", "name": "type_params", "type": {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Compare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Compare", "name": "Compare", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Compare", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Compare", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "ops", "comparators", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Compare.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "ops", "comparators", "kwargs"], "arg_types": ["_ast.Compare", "_ast.expr", {".class": "Instance", "args": ["_ast.cmpop"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Compare", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Compare.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ops"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "comparators"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "comparators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Compare.comparators", "name": "comparators", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Compare.left", "name": "left", "type": "_ast.expr"}}, "ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Compare.ops", "name": "ops", "type": {".class": "Instance", "args": ["_ast.cmpop"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Constant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Constant", "name": "Constant", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Constant", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Constant", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "kind", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Constant.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "kind", "kwargs"], "arg_types": ["_ast.Constant", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Constant", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Constant.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kind"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Constant.kind", "name": "kind", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "n": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Constant.n", "name": "n", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": true}}}, "s": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Constant.s", "name": "s", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Constant.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Continue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Continue", "name": "Continue", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Continue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Continue", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Del": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Del", "name": "Del", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Del", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Del", "_ast.expr_context", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Delete", "name": "Delete", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Delete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Delete", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "targets", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Delete.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "targets", "kwargs"], "arg_types": ["_ast.Delete", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Delete", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Delete.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "targets"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Delete.targets", "name": "targets", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Dict", "name": "Dict", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Dict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Dict", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "keys", "values", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Dict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "keys", "values", "kwargs"], "arg_types": ["_ast.Dict", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Dict.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Dict.keys", "name": "keys", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Dict.values", "name": "values", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DictComp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.DictComp", "name": "DictComp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.DictComp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.DictComp", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "key", "value", "generators", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.DictComp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "key", "value", "generators", "kwargs"], "arg_types": ["_ast.DictComp", "_ast.expr", "_ast.expr", {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DictComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.DictComp.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "key"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "generators"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.DictComp.generators", "name": "generators", "type": {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.DictComp.key", "name": "key", "type": "_ast.expr"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.DictComp.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Div": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Div", "name": "Div", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Div", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Div", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Eq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Eq", "name": "Eq", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Eq", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Eq", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.excepthandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON><PERSON><PERSON>", "_ast.excepthandler", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ExceptHandler.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ExceptHandler.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ExceptHandler.type", "name": "type", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Expr", "name": "Expr", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Expr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Expr", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Expr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["_ast.Expr", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Expr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Expr.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Expr.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Expression", "name": "Expression", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Expression", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Expression", "_ast.mod", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Expression.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["_ast.Expression", "_ast.expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Expression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Expression.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Expression.body", "name": "body", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FloorDiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.FloorDiv", "name": "FloorDiv", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.FloorDiv", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.FloorDiv", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "For": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.For", "name": "For", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.For", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.For", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.For.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "arg_types": ["_ast.For", "_ast.expr", "_ast.expr", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of For", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.For.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "target"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "iter"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "orelse"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.For.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.For.iter", "name": "iter", "type": "_ast.expr"}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.<PERSON>.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.For.target", "name": "target", "type": "_ast.expr"}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.For.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FormattedValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.FormattedValue", "name": "FormattedValue", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.FormattedValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.FormattedValue", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "conversion", "format_spec", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.FormattedValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "conversion", "format_spec", "kwargs"], "arg_types": ["_ast.FormattedValue", "_ast.expr", "builtins.int", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FormattedValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.FormattedValue.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "conversion"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "format_spec"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "conversion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FormattedValue.conversion", "name": "conversion", "type": "builtins.int"}}, "format_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FormattedValue.format_spec", "name": "format_spec", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FormattedValue.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.FunctionDef", "name": "FunctionDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.FunctionDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.FunctionDef", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.FunctionDef.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.FunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.FunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.FunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.FunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.FunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.FunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.FunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.FunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.FunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 3, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "type_params", "kwargs"], "arg_types": ["_ast.FunctionDef", "builtins.str", "_ast.arguments", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.FunctionDef.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "args"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "decorator_list"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "returns"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_params"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionDef.args", "name": "args", "type": "_ast.arguments"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionDef.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "decorator_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionDef.decorator_list", "name": "decorator_list", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionDef.name", "name": "name", "type": "builtins.str"}}, "returns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionDef.returns", "name": "returns", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionDef.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionDef.type_params", "name": "type_params", "type": {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.FunctionType", "name": "FunctionType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.FunctionType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.FunctionType", "_ast.mod", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "argtypes", "returns"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.FunctionType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "argtypes", "returns"], "arg_types": ["_ast.FunctionType", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "_ast.expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.FunctionType.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "argtypes"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "returns"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "argtypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionType.argtypes", "name": "argtypes", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "returns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.FunctionType.returns", "name": "returns", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GeneratorExp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.GeneratorExp", "name": "GeneratorExp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.GeneratorExp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.GeneratorExp", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.GeneratorExp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "arg_types": ["_ast.GeneratorExp", "_ast.expr", {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GeneratorExp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.GeneratorExp.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "elt"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "generators"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "elt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.GeneratorExp.elt", "name": "elt", "type": "_ast.expr"}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.GeneratorExp.generators", "name": "generators", "type": {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Global": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Global", "name": "Global", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Global", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Global", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Global.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "arg_types": ["_ast.Global", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Global", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Global.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "names"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Global.names", "name": "names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Gt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Gt", "name": "Gt", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Gt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Gt", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GtE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.GtE", "name": "GtE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.GtE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.GtE", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "If": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.If", "name": "If", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.If", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.If", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.If.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "arg_types": ["_ast.If", "_ast.expr", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of If", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.If.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "test"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "orelse"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.If.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.If.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.If.test", "name": "test", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IfExp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.IfExp", "name": "IfExp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.IfExp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.IfExp", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.IfExp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "arg_types": ["_ast.IfExp", "_ast.expr", "_ast.expr", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IfExp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.IfExp.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "test"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "orelse"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.IfExp.body", "name": "body", "type": "_ast.expr"}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.IfExp.orelse", "name": "orelse", "type": "_ast.expr"}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.IfExp.test", "name": "test", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Import": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Import", "name": "Import", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Import", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Import", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Import.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "arg_types": ["_ast.Import", {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Import", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Import.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "names"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Import.names", "name": "names", "type": {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImportFrom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.ImportFrom", "name": "ImportFrom", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.ImportFrom", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.ImportFrom", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.ImportFrom.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["_ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["_ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["_ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["_ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["_ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["_ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.ImportFrom.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "names"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "level"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ImportFrom.level", "name": "level", "type": "builtins.int"}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ImportFrom.module", "name": "module", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ImportFrom.names", "name": "names", "type": {".class": "Instance", "args": ["_ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "In": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.In", "name": "In", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.In", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.In", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Interactive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Interactive", "name": "Interactive", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Interactive", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Interactive", "_ast.mod", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Interactive.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["_ast.Interactive", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Interactive", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Interactive.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Interactive.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Invert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Invert", "name": "Invert", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Invert", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Invert", "_ast.unaryop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Is": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Is", "name": "Is", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Is", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Is", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IsNot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.IsNot", "name": "IsNot", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.IsNot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.IsNot", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JoinedStr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.JoinedStr", "name": "JoinedStr", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.JoinedStr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.JoinedStr", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "values", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.JoinedStr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "values", "kwargs"], "arg_types": ["_ast.JoinedStr", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JoinedStr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.JoinedStr.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.JoinedStr.values", "name": "values", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LShift": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.LShift", "name": "LShift", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.LShift", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.LShift", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Lambda", "name": "Lambda", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Lambda", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Lambda", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "args", "body", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Lamb<PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "args", "body", "kwargs"], "arg_types": ["_ast.Lambda", "_ast.arguments", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Lambda", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Lamb<PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "args"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Lambda.args", "name": "args", "type": "_ast.arguments"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Lambda.body", "name": "body", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.List", "name": "List", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.List", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.List", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.List.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "arg_types": ["_ast.List", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "_ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of List", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.List.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "elts"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ctx"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.List.ctx", "name": "ctx", "type": "_ast.expr_context"}}, "elts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.List.elts", "name": "elts", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListComp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.ListComp", "name": "ListComp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.ListComp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.ListComp", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.ListComp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "arg_types": ["_ast.ListComp", "_ast.expr", {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.ListComp.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "elt"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "generators"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "elt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ListComp.elt", "name": "elt", "type": "_ast.expr"}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ListComp.generators", "name": "generators", "type": {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Load": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Load", "name": "Load", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Load", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Load", "_ast.expr_context", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Lt", "name": "Lt", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Lt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Lt", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LtE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.LtE", "name": "LtE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.LtE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.LtE", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatMult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON>", "name": "MatMult", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON>", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Match", "name": "Match", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Match", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Match", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "subject", "cases", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Match.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "subject", "cases", "kwargs"], "arg_types": ["_ast.Match", "_ast.expr", {".class": "Instance", "args": ["_ast.match_case"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Match", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Match.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "subject"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cases"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "cases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Match.cases", "name": "cases", "type": {".class": "Instance", "args": ["_ast.match_case"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "subject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Match.subject", "name": "subject", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchAs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchAs", "name": "MatchAs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchAs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchAs", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "pattern", "name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchAs.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "pattern", "name", "kwargs"], "arg_types": ["_ast.MatchAs", {".class": "UnionType", "items": ["_ast.pattern", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchAs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.MatchAs.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pattern"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchAs.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchAs.pattern", "name": "pattern", "type": {".class": "UnionType", "items": ["_ast.pattern", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchClass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchClass", "name": "MatchClass", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchClass", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "cls", "patterns", "kwd_attrs", "kwd_patterns", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchClass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "cls", "patterns", "kwd_attrs", "kwd_patterns", "kwargs"], "arg_types": ["_ast.MatchClass", "_ast.expr", {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchClass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.MatchClass.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cls"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "patterns"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kwd_attrs"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kwd_patterns"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchClass.cls", "name": "cls", "type": "_ast.expr"}}, "kwd_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchClass.kwd_attrs", "name": "kwd_attrs", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "kwd_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchClass.kwd_patterns", "name": "kwd_patterns", "type": {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchClass.patterns", "name": "patterns", "type": {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchMapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchMapping", "name": "MatchMapping", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchMapping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchMapping", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "keys", "patterns", "rest", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchMapping.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "keys", "patterns", "rest", "kwargs"], "arg_types": ["_ast.MatchMapping", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchMapping", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.MatchMapping.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "patterns"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "rest"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchMapping.keys", "name": "keys", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchMapping.patterns", "name": "patterns", "type": {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "rest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchMapping.rest", "name": "rest", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchOr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchOr", "name": "MatchOr", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchOr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchOr", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "patterns", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchOr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "patterns", "kwargs"], "arg_types": ["_ast.MatchOr", {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchOr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.MatchOr.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "patterns"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchOr.patterns", "name": "patterns", "type": {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchSequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchSequence", "name": "MatchSequence", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchSequence", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchSequence", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "patterns", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchSequence.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "patterns", "kwargs"], "arg_types": ["_ast.MatchSequence", {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchSequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.MatchSequence.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "patterns"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchSequence.patterns", "name": "patterns", "type": {".class": "Instance", "args": ["_ast.pattern"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchSingleton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchSingleton", "name": "MatchSingleton", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchSingleton", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchSingleton", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchSingleton.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["_ast.MatchSingleton", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchSingleton", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Match<PERSON>ing<PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchSingleton.value", "name": "value", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchStar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchStar", "name": "MatchStar", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchStar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchStar", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchStar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "arg_types": ["_ast.MatchStar", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchStar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.MatchStar.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchStar.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.MatchValue", "name": "MatchValue", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.MatchValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.MatchValue", "_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.MatchValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["_ast.MatchValue", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatchValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.MatchValue.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.MatchValue.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON>d", "name": "Mod", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON>d", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON>d", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON><PERSON>", "_ast.mod", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "type_ignores"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "type_ignores"], "arg_types": ["_ast.<PERSON><PERSON><PERSON>", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.TypeIgnore"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Module", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_ignores"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Module.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_ignores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Module.type_ignores", "name": "type_ignores", "type": {".class": "Instance", "args": ["_ast.TypeIgnore"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Mult", "name": "<PERSON>lt", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Mult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Mult", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Name", "name": "Name", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Name", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Name", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "id", "ctx", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Name.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "id", "ctx", "kwargs"], "arg_types": ["_ast.Name", "builtins.str", "_ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Name", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Name.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ctx"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Name.ctx", "name": "ctx", "type": "_ast.expr_context"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Name.id", "name": "id", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NamedExpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.NamedExpr", "name": "NamedExpr", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.NamedExpr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.NamedExpr", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.NamedExpr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "value", "kwargs"], "arg_types": ["_ast.NamedExpr", "_ast.Name", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NamedExpr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.NamedExpr.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "target"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.NamedExpr.target", "name": "target", "type": "_ast.Name"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.NamedExpr.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Nonlocal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Nonlocal", "name": "Nonlocal", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Nonlocal", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Nonlocal", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Nonlocal.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "arg_types": ["_ast.Nonlocal", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Nonlocal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Nonlocal.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "names"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Nonlocal.names", "name": "names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Not": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Not", "name": "Not", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Not", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Not", "_ast.unaryop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotEq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.NotEq", "name": "NotEq", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.NotEq", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.NotEq", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotIn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.NotIn", "name": "NotIn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.NotIn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.NotIn", "_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Or": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.boolop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Or", "name": "Or", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Or", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Or", "_ast.boolop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParamSpec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.type_param"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.ParamSpec", "name": "ParamSpec", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.ParamSpec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.ParamSpec", "_ast.type_param", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.ParamSpec.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "arg_types": ["_ast.ParamSpec", "builtins.str", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParamSpec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.ParamSpec.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.ParamSpec.name", "name": "name", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Pass", "name": "Pass", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Pass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Pass", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON>w", "name": "<PERSON>w", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON>w", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON>w", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyCF_ALLOW_TOP_LEVEL_AWAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.PyCF_ALLOW_TOP_LEVEL_AWAIT", "name": "PyCF_ALLOW_TOP_LEVEL_AWAIT", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}}}, "PyCF_ONLY_AST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.PyCF_ONLY_AST", "name": "PyCF_ONLY_AST", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}}}, "PyCF_TYPE_COMMENTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.PyCF_TYPE_COMMENTS", "name": "PyCF_TYPE_COMMENTS", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4096}}}, "RShift": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.RShift", "name": "RShift", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.RShift", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.RShift", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Raise": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON>", "name": "Rai<PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON>", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "exc", "cause", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.<PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "exc", "cause", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Raise", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "exc"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cause"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "cause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Raise.cause", "name": "cause", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "exc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Raise.exc", "name": "exc", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Return": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Return", "name": "Return", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Return", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Return", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Return.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["_ast.Return", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Return", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Return.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Return.value", "name": "value", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Set", "name": "Set", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Set", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Set", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "elts", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Set.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "elts", "kwargs"], "arg_types": ["_ast.Set", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Set", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Set.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "elts"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "elts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Set.elts", "name": "elts", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SetComp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON>omp", "name": "SetComp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON>omp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON>omp", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.<PERSON>Com<PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>omp", "_ast.expr", {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SetComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.SetComp.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "elt"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "generators"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "elt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.SetComp.elt", "name": "elt", "type": "_ast.expr"}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.SetComp.generators", "name": "generators", "type": {".class": "Instance", "args": ["_ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Slice", "name": "Slice", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Slice", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Slice", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "lower", "upper", "step", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Slice.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "lower", "upper", "step", "kwargs"], "arg_types": ["_ast.Slice", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Slice", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Slice.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "lower"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "upper"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "step"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "lower": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Slice.lower", "name": "lower", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Slice.step", "name": "step", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "upper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Slice.upper", "name": "upper", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Starred": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Starred", "name": "Starred", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Starred", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Starred", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "ctx", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.<PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "ctx", "kwargs"], "arg_types": ["_ast.Starred", "_ast.expr", "_ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Starr<PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ctx"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Starred.ctx", "name": "ctx", "type": "_ast.expr_context"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Starred.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Store": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Store", "name": "Store", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Store", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Store", "_ast.expr_context", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Sub", "name": "Sub", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Sub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Sub", "_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Subscript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Subscript", "name": "Subscript", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Subscript", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Subscript", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "slice", "ctx", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Subscript.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "slice", "ctx", "kwargs"], "arg_types": ["_ast.Subscript", "_ast.expr", "_ast.expr", "_ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Subscript", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Subscript.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "slice"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ctx"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Subscript.ctx", "name": "ctx", "type": "_ast.expr_context"}}, "slice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Subscript.slice", "name": "slice", "type": "_ast.expr"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Subscript.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Try": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.Try", "name": "Try", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.Try", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.Try", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "body", "handlers", "orelse", "finalbody", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.Try.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "body", "handlers", "orelse", "finalbody", "kwargs"], "arg_types": ["_ast.Try", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.<PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Try", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.Try.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "handlers"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "orelse"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "finalbody"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Try.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "finalbody": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Try.finalbody", "name": "finalbody", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Try.handlers", "name": "handlers", "type": {".class": "Instance", "args": ["_ast.<PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.<PERSON>.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TryStar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.TryStar", "name": "TryStar", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.TryStar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.TryStar", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "body", "handlers", "orelse", "finalbody", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.TryStar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "body", "handlers", "orelse", "finalbody", "kwargs"], "arg_types": ["_ast.TryStar", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.<PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TryStar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.TryStar.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "handlers"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "orelse"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "finalbody"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TryStar.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "finalbody": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TryStar.finalbody", "name": "finalbody", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TryStar.handlers", "name": "handlers", "type": {".class": "Instance", "args": ["_ast.<PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TryStar.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON>", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON>", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.<PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "_ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "elts"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ctx"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Tuple.ctx", "name": "ctx", "type": "_ast.expr_context"}}, "dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.<PERSON>ple.dims", "name": "dims", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "elts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Tuple.elts", "name": "elts", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.TypeAlias", "name": "TypeAlias", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.TypeAlias", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.TypeAlias", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "type_params", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.TypeAlias.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "type_params", "value", "kwargs"], "arg_types": ["_ast.TypeAlias", "_ast.Name", {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}, "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypeAlias", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.TypeA<PERSON>s.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_params"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeAlias.name", "name": "name", "type": "_ast.Name"}}, "type_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeAlias.type_params", "name": "type_params", "type": {".class": "Instance", "args": ["_ast.type_param"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeAlias.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeIgnore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.type_ignore"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.TypeIgnore", "name": "TypeIgnore", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.TypeIgnore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.TypeIgnore", "_ast.type_ignore", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lineno", "tag"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.TypeIgnore.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lineno", "tag"], "arg_types": ["_ast.TypeIgnore", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypeIgnore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.TypeIgnore.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "lineno"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "tag"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeIgnore.lineno", "name": "lineno", "type": "builtins.int"}}, "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeIgnore.tag", "name": "tag", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.type_param"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.TypeVar", "name": "TypeVar", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.TypeVar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.TypeVar", "_ast.type_param", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "bound", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.TypeVar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "bound", "kwargs"], "arg_types": ["_ast.TypeVar", "builtins.str", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypeVar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.TypeVar.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bound"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "bound": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeVar.bound", "name": "bound", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeVar.name", "name": "name", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVarTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.type_param"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.TypeVarTuple", "name": "TypeVarTuple", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.TypeVarTuple", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.TypeVarTuple", "_ast.type_param", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.TypeVarTuple.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "arg_types": ["_ast.TypeVarTuple", "builtins.str", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypeVarTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.TypeVarTuple.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.TypeVarTuple.name", "name": "name", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UAdd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.UAdd", "name": "UAdd", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.UAdd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.UAdd", "_ast.unaryop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "USub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON>ub", "name": "USub", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON>ub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON>ub", "_ast.unaryop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnaryOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON>ry<PERSON>p", "name": "UnaryOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON>ry<PERSON>p", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON>ry<PERSON>p", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "operand", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.<PERSON>ryOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "operand", "kwargs"], "arg_types": ["_ast.<PERSON>ry<PERSON>p", "_ast.unaryop", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnaryOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON>ryOp.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "op"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "operand"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.UnaryOp.op", "name": "op", "type": "_ast.unaryop"}}, "operand": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.UnaryOp.operand", "name": "operand", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "While": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.While", "name": "While", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.While", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.While", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.While.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "arg_types": ["_ast.While", "_ast.expr", {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of While", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.While.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "test"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "orelse"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.While.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.<PERSON>.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.While.test", "name": "test", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "With": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.With", "name": "With", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.With", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.With", "_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.With.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "arg_types": ["_ast.With", {".class": "Instance", "args": ["_ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of With", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.With.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "items"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.With.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.With.items", "name": "items", "type": {".class": "Instance", "args": ["_ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.With.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Yield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON>", "name": "Yield", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON>", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.<PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Yield", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.Yield.value", "name": "value", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "YieldFrom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.<PERSON><PERSON>", "name": "YieldFrom", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.<PERSON><PERSON>", "_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.YieldFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["_ast.<PERSON><PERSON>", "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of YieldFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.<PERSON><PERSON><PERSON><PERSON>.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.YieldFrom.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Attributes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast._Attributes", "name": "_Attributes", "type_vars": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "_ast._EndPositionT", "id": 1, "name": "_EndPositionT", "namespace": "_ast._Attributes", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "_ast._Attributes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast._Attributes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_EndPositionT"], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "_ast._EndPositionT", "id": 1, "name": "_EndPositionT", "namespace": "_ast._Attributes", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}], ["end_col_offset", {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "_ast._EndPositionT", "id": 1, "name": "_EndPositionT", "namespace": "_ast._Attributes", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}]], "required_keys": []}}}, "_EndPositionT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "_ast._EndPositionT", "name": "_EndPositionT", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}}, "_Identifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_ast._Identifier", "line": 20, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "_Pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "_ast._<PERSON><PERSON>", "line": 1060, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "_ast.pattern"}}, "_Slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "_ast._Slice", "line": 777, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "_ast.expr"}}, "_SliceAttributes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "_ast._SliceAttributes", "line": 778, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": ["builtins.int"], "type_ref": "_ast._Attributes"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "alias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.alias", "name": "alias", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.alias", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.alias", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "asname", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.alias.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "asname", "kwargs"], "arg_types": ["_ast.alias", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of alias", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.alias.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "asname"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "asname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.alias.asname", "name": "asname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.alias.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.alias.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.alias.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.alias.lineno", "name": "lineno", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.alias.name", "name": "name", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "arg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.arg", "name": "arg", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.arg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.arg", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "arg", "annotation", "type_comment", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.arg.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "arg", "annotation", "type_comment", "kwargs"], "arg_types": ["_ast.arg", "builtins.str", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.arg.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "arg"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "annotation"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "type_comment"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arg.annotation", "name": "annotation", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arg.arg", "name": "arg", "type": "builtins.str"}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arg.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arg.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arg.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arg.lineno", "name": "lineno", "type": "builtins.int"}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arg.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.arguments", "name": "arguments", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.arguments", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.arguments", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.arguments.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["_ast.arguments", {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.arguments.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "posonlyargs"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "args"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "vararg"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kwonlyargs"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kw_defaults"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kwarg"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "defaults"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arguments.args", "name": "args", "type": {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arguments.defaults", "name": "defaults", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "kw_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arguments.kw_defaults", "name": "kw_defaults", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "kwarg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arguments.kwarg", "name": "kwarg", "type": {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "kwonlyargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arguments.kwonlyargs", "name": "kwonlyargs", "type": {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "posonlyargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arguments.posonlyargs", "name": "posonlyargs", "type": {".class": "Instance", "args": ["_ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "vararg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.arguments.vararg", "name": "vararg", "type": {".class": "UnionType", "items": ["_ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "boolop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.boolop", "name": "boolop", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.boolop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.boolop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "cmpop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.cmpop", "name": "cmpop", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.cmpop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.cmpop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "comprehension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.comprehension", "name": "comprehension", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.comprehension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.comprehension", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "target", "iter", "ifs", "is_async"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.comprehension.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "target", "iter", "ifs", "is_async"], "arg_types": ["_ast.comprehension", "_ast.expr", "_ast.expr", {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of comprehension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.comprehension.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "target"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "iter"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ifs"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "is_async"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ifs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.comprehension.ifs", "name": "ifs", "type": {".class": "Instance", "args": ["_ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "is_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.comprehension.is_async", "name": "is_async", "type": "builtins.int"}}, "iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.comprehension.iter", "name": "iter", "type": "_ast.expr"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.comprehension.target", "name": "target", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "excepthandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.excepthandler", "name": "except<PERSON>ler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.excepthandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.excepthandler", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.excepthandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["_ast.excepthandler", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of excepthandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.excepthandler.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.excepthandler.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.excepthandler.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.excepthandler.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.expr", "name": "expr", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.expr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.expr", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.expr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of expr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.expr.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.expr.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.expr.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.expr.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "expr_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.expr_context", "name": "expr_context", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.expr_context", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.expr_context", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "keyword": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.keyword", "name": "keyword", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.keyword", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.keyword", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.keyword.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["_ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["_ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["_ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["_ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["_ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["_ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "_ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.keyword.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "arg"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.keyword.arg", "name": "arg", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.keyword.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.keyword.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.keyword.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.keyword.lineno", "name": "lineno", "type": "builtins.int"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.keyword.value", "name": "value", "type": "_ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "match_case": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.match_case", "name": "match_case", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.match_case", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.match_case", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "_ast.match_case.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pattern", "guard", "body"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.match_case.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pattern", "guard", "body"], "arg_types": ["_ast.match_case", "_ast.pattern", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of match_case", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.match_case.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pattern", "guard", "body"], "arg_types": ["_ast.match_case", "_ast.pattern", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of match_case", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 3], "arg_names": ["self", "pattern", "guard", "body"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ast.match_case.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3], "arg_names": ["self", "pattern", "guard", "body"], "arg_types": ["_ast.match_case", "_ast.pattern", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of match_case", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ast.match_case.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3], "arg_names": ["self", "pattern", "guard", "body"], "arg_types": ["_ast.match_case", "_ast.pattern", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of match_case", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pattern", "guard", "body"], "arg_types": ["_ast.match_case", "_ast.pattern", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of match_case", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 3], "arg_names": ["self", "pattern", "guard", "body"], "arg_types": ["_ast.match_case", "_ast.pattern", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of match_case", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.match_case.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pattern"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "guard"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.match_case.body", "name": "body", "type": {".class": "Instance", "args": ["_ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "guard": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.match_case.guard", "name": "guard", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.match_case.pattern", "name": "pattern", "type": "_ast.pattern"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "mod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.mod", "name": "mod", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.mod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.mod", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "operator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.operator", "name": "operator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.operator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.operator", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.pattern", "name": "pattern", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.pattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.pattern", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.pattern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["_ast.pattern", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of pattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.pattern.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.pattern.end_col_offset", "name": "end_col_offset", "type": "builtins.int"}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.pattern.end_lineno", "name": "end_lineno", "type": "builtins.int"}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.pattern.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stmt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.stmt", "name": "stmt", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.stmt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.stmt", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.stmt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["_ast.stmt", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stmt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.stmt.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.stmt.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.stmt.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.stmt.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_ignore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.type_ignore", "name": "type_ignore", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.type_ignore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.type_ignore", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "type_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.type_param", "name": "type_param", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.type_param", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.type_param", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.type_param.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["_ast.type_param", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", "builtins.int"], ["end_col_offset", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of type_param", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.type_param.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.type_param.end_col_offset", "name": "end_col_offset", "type": "builtins.int"}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.type_param.end_lineno", "name": "end_lineno", "type": "builtins.int"}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.type_param.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unaryop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.unaryop", "name": "unaryop", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.unaryop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.unaryop", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "withitem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ast.withitem", "name": "withitem", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "_ast.withitem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ast", "mro": ["_ast.withitem", "_ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "context_expr", "optional_vars"], "dataclass_transform_spec": null, "flags": [], "fullname": "_ast.withitem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "context_expr", "optional_vars"], "arg_types": ["_ast.withitem", "_ast.expr", {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of withitem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_ast.withitem.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "context_expr"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "optional_vars"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "context_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.withitem.context_expr", "name": "context_expr", "type": "_ast.expr"}}, "optional_vars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ast.withitem.optional_vars", "name": "optional_vars", "type": {".class": "UnionType", "items": ["_ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/_ast.pyi"}