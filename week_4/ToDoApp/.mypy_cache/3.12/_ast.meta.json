{"data_mtime": 1750014896, "dep_lines": [1, 2, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sys", "typing_extensions", "typing", "builtins", "_collections_abc", "_typeshed", "abc", "importlib", "importlib.machinery", "types"], "hash": "4eea76aa4413136970222d718a47b0ae8516ed2a0a75cc1734d7ae680b363c17", "id": "_ast", "ignore_all": true, "interface_hash": "672c2d6e5d10d9394dfdae7ac00d7360a4cc8e45e4a7d25c06de825306c4869e", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/_ast.pyi", "plugin_data": null, "size": 43159, "suppressed": [], "version_id": "1.11.2"}