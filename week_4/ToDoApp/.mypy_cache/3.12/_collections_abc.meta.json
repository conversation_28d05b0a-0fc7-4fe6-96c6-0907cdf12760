{"data_mtime": 1750014896, "dep_lines": [1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["sys", "abc", "types", "typing", "builtins", "_typeshed", "importlib", "importlib.machinery"], "hash": "0aa5f4e5583d6793ff838ee1c4ce7dc8b6e8e8171fff984eb0a5ebca4fe1a3dc", "id": "_collections_abc", "ignore_all": true, "interface_hash": "23599dd64d0e6c2faf2447cb79bea578481daac5b405c3569ba93925716a6d44", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/_collections_abc.pyi", "plugin_data": null, "size": 2479, "suppressed": [], "version_id": "1.11.2"}