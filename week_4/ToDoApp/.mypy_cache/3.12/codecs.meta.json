{"data_mtime": 1750014896, "dep_lines": [5, 1, 2, 3, 4, 6, 7, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "types", "_codecs", "_typeshed", "abc", "typing", "typing_extensions", "builtins", "_collections_abc", "importlib", "importlib.machinery"], "hash": "d8b26fa9d5eb2ecd005ff9cdba74ff10c105658bba18398fed78fcd80cf1fc9e", "id": "codecs", "ignore_all": true, "interface_hash": "8b873fdbf8f5fa997fbe2a808a574a6e2924d0dd6654175f2da3c355b38c1237", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/codecs.pyi", "plugin_data": null, "size": 11812, "suppressed": [], "version_id": "1.11.2"}