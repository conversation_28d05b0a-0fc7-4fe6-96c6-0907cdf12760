{"data_mtime": 1750014896, "dep_lines": [6, 7, 27, 1, 2, 3, 4, 5, 8, 9, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "importlib.machinery", "importlib._abc", "_ast", "sys", "types", "_typeshed", "abc", "io", "typing", "builtins", "_collections_abc", "os"], "hash": "71fd2d0fabd6c9cc287805b54902fa48293adba65c8b4f085d55afe58c55eaec", "id": "importlib.abc", "ignore_all": true, "interface_hash": "682ee1ec33c2f8550f70b4406c4e2e5ee7696dc22e796986e45e99f149e4161e", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/importlib/abc.pyi", "plugin_data": null, "size": 6933, "suppressed": [], "version_id": "1.11.2"}