{"data_mtime": 1750014896, "dep_lines": [1, 5, 6, 162, 1, 2, 3, 4, 7, 8, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 20, 10, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["importlib.abc", "collections.abc", "importlib.metadata", "importlib.readers", "importlib", "sys", "types", "_typeshed", "typing", "typing_extensions", "builtins", "_collections_abc", "abc", "importlib._abc"], "hash": "a12bf36c76b296fbea03154ecdb37c46428c106960cd08af038ecc6b7df98b72", "id": "importlib.machinery", "ignore_all": true, "interface_hash": "43831cef6d35742eef043accc12e51bec0e0459d5d21c60b55e1c94188900af3", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/importlib/machinery.pyi", "plugin_data": null, "size": 7004, "suppressed": [], "version_id": "1.11.2"}