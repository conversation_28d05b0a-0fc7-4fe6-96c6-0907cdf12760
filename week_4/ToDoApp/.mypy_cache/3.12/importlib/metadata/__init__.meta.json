{"data_mtime": 1750014896, "dep_lines": [36, 6, 7, 8, 1, 2, 3, 4, 5, 9, 11, 12, 13, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["importlib.metadata._meta", "collections.abc", "email.message", "importlib.abc", "abc", "pathlib", "sys", "_collections_abc", "_typeshed", "os", "re", "typing", "typing_extensions", "builtins", "importlib.machinery", "types"], "hash": "89f99d4952cc61131e4bbe69c9bd79541d7f410d4bbcc27cd39d4c19022aa2a0", "id": "importlib.metadata", "ignore_all": true, "interface_hash": "39c9a209fe58e559c497d040c7c0d65738d7bb600da5ae1732c19de7a9363185", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/importlib/metadata/__init__.pyi", "plugin_data": null, "size": 9391, "suppressed": [], "version_id": "1.11.2"}