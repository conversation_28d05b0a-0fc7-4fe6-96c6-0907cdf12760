{"data_mtime": 1750014896, "dep_lines": [6, 1, 2, 3, 4, 5, 7, 8, 9, 10, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "abc", "builtins", "codecs", "sys", "_typeshed", "os", "types", "typing", "typing_extensions", "_collections_abc", "importlib", "importlib.machinery"], "hash": "c3a83a386dc6fe1992291d35989adb51772eb0ba2b33da500780a509ced93325", "id": "io", "ignore_all": true, "interface_hash": "76c3d10be5c2fa3a1c414184adf4c3b630766afb0757251d14f5834923e5a261", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/io.pyi", "plugin_data": null, "size": 9682, "suppressed": [], "version_id": "1.11.2"}