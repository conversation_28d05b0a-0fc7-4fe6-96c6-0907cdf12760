{"data_mtime": 1750014955, "dep_lines": [1, 2, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["task_list", "datetime", "task", "builtins", "_collections_abc", "_typeshed", "abc", "importlib", "importlib.machinery", "typing"], "hash": "c8feb6f88c68af54d68edbdcaf97f8b7e4915ff1664f435dfe71ca4e56faeb64", "id": "main", "ignore_all": false, "interface_hash": "d95d30990e9f4ed01f9b56a5a1c89cfb2481fcba7404d67b0a7fa97472e36d2f", "mtime": 1750014925, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "main.py", "plugin_data": null, "size": 4659, "suppressed": [], "version_id": "1.11.2"}