{"data_mtime": 1750014896, "dep_lines": [24, 45, 1, 2, 22, 23, 25, 26, 27, 28, 43, 48, 993, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "os.path", "sys", "_typeshed", "abc", "builtins", "contextlib", "io", "subprocess", "typing", "typing_extensions", "types", "resource", "_collections_abc", "importlib", "importlib.machinery"], "hash": "b41f76434bc66aa4558392af67be9fa67a9a19c7c72ea21ee1e0842da99546f5", "id": "os", "ignore_all": true, "interface_hash": "85881737cdbf7681e09df8360eea88b42bb584f79df66589e97e5fe40b30f959", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/os/__init__.pyi", "plugin_data": null, "size": 40401, "suppressed": [], "version_id": "1.11.2"}