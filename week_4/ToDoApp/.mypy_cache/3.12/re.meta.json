{"data_mtime": 1750014896, "dep_lines": [6, 1, 2, 3, 4, 5, 8, 9, 12, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "enum", "sre_compile", "sre_constants", "sys", "_typeshed", "typing", "typing_extensions", "types", "builtins", "_collections_abc", "abc", "importlib", "importlib.machinery"], "hash": "fe134962b9b76f88daecb6b3a956f80b1f21ec14674828ec8e8fe0e1e6784604", "id": "re", "ignore_all": true, "interface_hash": "aab61979ee0508859bd5edf6d53041fc7a00db01f5f3f0f53a717a7a619f14e7", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/re.pyi", "plugin_data": null, "size": 11606, "suppressed": [], "version_id": "1.11.2"}