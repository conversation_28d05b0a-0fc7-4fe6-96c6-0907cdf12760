{"data_mtime": 1750014896, "dep_lines": [1, 2, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["sys", "typing", "typing_extensions", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "types"], "hash": "cfb8327ad6d1f82090949665027111e9f4f54c3e1dc0b5baec0b377845590a5c", "id": "sre_constants", "ignore_all": true, "interface_hash": "f68b451aa1f0d40541a7733a51b3efdc9c24fc7ea55b40ff8d095b9c519884df", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/sre_constants.pyi", "plugin_data": null, "size": 3986, "suppressed": [], "version_id": "1.11.2"}