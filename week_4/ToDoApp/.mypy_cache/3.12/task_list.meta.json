{"id": "task_list", "path": "task_list.py", "mtime": 1750014596, "size": 806, "hash": "27585bd04786160096f78afa192cd49668a84fa66dabca4ba92813068519cd30", "data_mtime": 1750014896, "dependencies": ["datetime", "task", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "typing"], "suppressed": [], "options": {"disallow_any_decorated": false, "follow_imports_for_stubs": false, "local_partial_types": false, "follow_imports": "normal", "disallow_any_unimported": false, "enable_error_code": [], "disallow_untyped_calls": true, "strict_concatenate": false, "implicit_reexport": false, "ignore_missing_imports": false, "extra_checks": true, "disallow_any_generics": true, "disable_bytearray_promotion": false, "allow_untyped_globals": false, "always_false": [], "disallow_untyped_defs": true, "platform": "darwin", "disabled_error_codes": [], "disable_error_code": [], "enabled_error_codes": [], "disallow_untyped_decorators": true, "allow_redefinition": false, "always_true": [], "strict_equality": true, "strict_optional": true, "bazel": false, "plugins": [], "mypyc": false, "disallow_subclassing_any": true, "warn_unreachable": false, "disallow_incomplete_defs": true, "disallow_any_expr": false, "ignore_errors": false, "warn_return_any": true, "implicit_optional": false, "check_untyped_defs": true, "warn_unused_ignores": true, "old_type_inference": false, "disable_memoryview_promotion": false, "disallow_any_explicit": false, "warn_no_return": true}, "dep_prios": [10, 5, 5, 30, 30, 30, 30, 30], "dep_lines": [2, 3, 1, 1, 1, 1, 1, 1], "interface_hash": "a5d8b0ab86f43be363136765180061170984bda4d69aae6ef84c34d7786ba41f", "version_id": "1.11.2", "ignore_all": false, "plugin_data": null}