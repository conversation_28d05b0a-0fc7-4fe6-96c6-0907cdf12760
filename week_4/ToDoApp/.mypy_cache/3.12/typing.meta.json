{"data_mtime": 1750014896, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 480, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections", "sys", "typing_extensions", "_collections_abc", "_typeshed", "abc", "re", "types", "contextlib", "builtins", "importlib", "importlib.machinery"], "hash": "1910f6a6460c9c6522be72184fef054eb0a77b7a8c91876fd14e7870dbceb53c", "id": "typing", "ignore_all": true, "interface_hash": "9178e7a7fa3e6ee7bc36f4d8b4fa5e5de50273328c05407509e051f49c3e571f", "mtime": 1725578489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/opt/anaconda3/lib/python3.12/site-packages/mypy/typeshed/stdlib/typing.pyi", "plugin_data": null, "size": 37686, "suppressed": [], "version_id": "1.11.2"}