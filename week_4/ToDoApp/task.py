# Task class

# Importing datetime module(Python Libraries)
import datetime

class Task:
    def __init__(self, title: str, date_due: datetime.datetime):
        self.title = title
        self.date_created = datetime.datetime.now()
        self.date_due = date_due
        self.completed = False
    
    def __str__(self) -> str:
        return f"{self.title} {"[Completed]" if self.completed else "[Not Completed]"} Created: {self.date_created} Due: {self.date_due}"
        
    def mark_as_completed(self) -> None:
        self.completed = True
        print(f"Task '{self.title}' is completed.")
    
    def change_title(self, new_title: str) -> None:
        self.title = new_title
        print(f"Task title changed to '{self.title}'")
    
    def change_date(self, due_date: datetime.datetime) -> None:
        self.date_due = due_date
        print(f"Task due date changed to '{self.date_due}'")