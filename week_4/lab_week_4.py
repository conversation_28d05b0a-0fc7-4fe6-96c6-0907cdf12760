class TaskList:
    def __init__(self, owner):
        self.owner = owner.title()
        self.tasks = []
    
    def add_task(self, task):
        self.tasks.append(task)
        print(f"Task '{task}' added.")
    
    def remove_task(self, ix: int):
        try:
            my_task = self.tasks[ix]
            del self.tasks[ix]
            print(f"Task '{my_task}' removed.")
        except IndexError:
            print("Please enter a valid number.")
            
    
    def view_tasks(self):
        if not self.tasks:
            print("No tasks in the list.")
        else:
            print("Current tasks:")
            for i, task in enumerate(self.tasks, start=1):
                print(f"{i}. {task}")
    
    def list_options(self):
        while True:
            print("\nTo-Do List Manager")
            print("1. Add a task")
            print("2. View tasks")
            print("3. Remove a task")
            print("4. Mark task as completed")
            print("5. Change task")
            print("6. Quit") 
            choice = input("Enter your choice: ")
            if choice == "1":
                title = input("Enter the task you want to add:  ")
                task = Task(title)
                self.add_task(task)
            elif choice == "2":
                self.view_tasks()
            elif choice == "3":
                self.view_tasks()
                if not self.tasks:
                    continue
                else:
                    ix = int(input("Enter the task number to remove: "))
                    self.remove_task(ix - 1)
            elif choice == "4":
                continue
            elif choice == "5":
                continue
            elif choice == "6":
                print("Exiting program. Goodbye!")
                break
            else:
                print("Invalid choice. Please try again.")

class Task:
    def __init__(self, title, completed=False):
        self.title = title
        self.completed = completed
    
    def __str__(self):
        return f"{self.title}"

    def mark_as_completed(self):
        self.completed = True
        print(f"Task '{self.title}' marked as completed.")
    
    def change_title(self, new_title):
        self.title = new_title
        print(f"Task title changed to '{self.title}'")

    

input_owner = input("Enter the name of the owner: ")
task_list = TaskList(input_owner)
print(f"Welcome {task_list.owner}")

""" input_new_owner = input("Enter the name of the new owner: ")
task_list.owner = input_new_owner
print(f"Welcome {task_list.owner}") """

task_list.list_options()
