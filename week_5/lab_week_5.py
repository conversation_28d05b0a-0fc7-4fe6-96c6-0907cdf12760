# Week 5: Inheritance, Super(), Kwargs, and Multiple Inheritance

# Simple Inheritance
class Vehicle:
    """Base vehicle class with common attributes and methods."""

    def __init__(self, colour, weight, max_speed, max_range=None, seats=None):
        self.colour = colour
        self.weight = weight
        self.max_speed = max_speed
        self.max_range = max_range
        self.seats = seats

    def move(self, speed):
        print(f"The vehicle is moving at {speed} km/h")


class Car(Vehicle):
    """Car class inheriting from Vehicle with additional form_factor attribute."""

    def __init__(self, colour, weight, max_speed, form_factor, max_range=None, seats=None):
        # Using super() to call parent constructor
        super().__init__(colour, weight, max_speed, max_range, seats)
        self.form_factor = form_factor

    def move(self, speed):
        print(f"The car is driving at {speed} km/h")

# Test simple inheritance
car = Car("blue", 1500, 250, "SUV")
car.move(150)


class Electric(Car):
    """Electric car class inheriting from Car."""

    def __init__(self, colour, weight, max_speed, form_factor, battery_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.battery_capacity = battery_capacity

    def move(self, speed):
        print(f"The electric car is driving at {speed} km/h")

class Petrol(Car):
    """Petrol car class inheriting from Car."""

    def __init__(self, colour, weight, max_speed, form_factor, fuel_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.fuel_capacity = fuel_capacity

    def move(self, speed):
        print(f"The petrol car is driving at {speed} km/h")

# Test super() function
electric_car = Electric("green", 1200, 200, "Hatchback", 100)
electric_car.move(100)
petrol_car = Petrol("red", 1500, 250, "SUV", 50)
petrol_car.move(150)
generic_vehicle = Vehicle("red", 1000, 200)
generic_vehicle.move(100)


# Task 1: Modify move method to include maximum range
class ElectricTask1(Car):
    """Electric car with enhanced move method showing range."""

    def __init__(self, colour, weight, max_speed, form_factor, battery_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.battery_capacity = battery_capacity

    def move(self, speed, maximum_range):
        print(f"The electric car is driving at {speed} km/h and has a maximum range of {maximum_range} km")

class PetrolTask1(Car):
    """Petrol car with enhanced move method showing range."""

    def __init__(self, colour, weight, max_speed, form_factor, fuel_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.fuel_capacity = fuel_capacity

    def move(self, speed, maximum_range):
        print(f"The petrol car is driving at {speed} km/h and has a maximum range of {maximum_range} km")

# Test Task 1
electric_car_t1 = ElectricTask1("green", 1200, 200, "Hatchback", 100)
electric_car_t1.move(100, 300)
petrol_car_t1 = PetrolTask1("red", 1500, 250, "SUV", 50)
petrol_car_t1.move(150, 400)


# Task 2: Using **kwargs to pass additional parameters
class ElectricTask2(Car):
    """Electric car using kwargs and accessing max_range from parent."""

    def __init__(self, colour, weight, max_speed, form_factor, battery_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.battery_capacity = battery_capacity

    def move(self, speed):
        range_info = self.max_range if self.max_range else "Unknown"
        print(f"The electric car is driving at {speed} km/h and has a maximum range of {range_info} km")

class PetrolTask2(Car):
    """Petrol car using kwargs and accessing max_range from parent."""

    def __init__(self, colour, weight, max_speed, form_factor, fuel_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.fuel_capacity = fuel_capacity

    def move(self, speed):
        range_info = self.max_range if self.max_range else "Unknown"
        print(f"The petrol car is driving at {speed} km/h and has a maximum range of {range_info} km")

# Test Task 2
petrol_car_t2 = PetrolTask2("red", 1500, 250, "SUV", 50, max_range=400)
petrol_car_t2.move(150)
electric_car_t2 = ElectricTask2("green", 1000, 200, "Hatchback", 100, max_range=500, seats=5)
electric_car_t2.move(100)
print(f"Electric car seats: {electric_car_t2.seats}")


# Task 3: Create Plane class hierarchy
class Plane(Vehicle):
    """Plane class inheriting from Vehicle with aviation-specific attributes."""

    def __init__(self, colour, weight, max_speed, num_engines, wingspan, **kwargs):
        super().__init__(colour, weight, max_speed, **kwargs)
        self.num_engines = num_engines
        self.wingspan = wingspan

    def move(self, speed):
        print(f"The plane is flying at {speed} km/h")

class Propeller(Plane):
    """Propeller plane class with propeller-specific attributes."""

    def __init__(self, colour, weight, max_speed, num_engines, wingspan, num_propellers, propeller_diameter, **kwargs):
        super().__init__(colour, weight, max_speed, num_engines, wingspan, **kwargs)
        self.num_propellers = num_propellers
        self.propeller_diameter = propeller_diameter

    def move(self, speed):
        print(f"The propeller plane is flying at {speed} km/h")

class Jet(Plane):
    """Jet plane class with jet-specific attributes."""

    def __init__(self, colour, weight, max_speed, num_engines, wingspan, num_wings, engine_thrust, **kwargs):
        super().__init__(colour, weight, max_speed, num_engines, wingspan, **kwargs)
        self.num_wings = num_wings
        self.engine_thrust = engine_thrust

    def move(self, speed):
        print(f"The jet is flying at {speed} km/h")

# Test Task 3
propeller_plane = Propeller("red", 1000, 200, 2, 15, 4, 100)
propeller_plane.move(100)
jet_plane = Jet("blue", 1000, 200, 2, 20, 2, 1000)
jet_plane.move(200)
generic_plane = Plane("green", 1000, 200, 2, 10)
generic_plane.move(100)


# Multiple Inheritance: FlyingCar inherits from both Car and Plane
class FlyingCar(Car, Plane):
    """Flying car demonstrating multiple inheritance from Car and Plane."""

    def __init__(self, colour, weight, max_speed, form_factor, wingspan, **kwargs):
        # Initialize Car attributes
        Car.__init__(self, colour, weight, max_speed, form_factor, **kwargs)
        # Initialize Plane-specific attributes manually to avoid conflicts
        self.wingspan = wingspan

    def move(self, speed):
        print(f"The flying car is driving or flying at {speed} km/h")


# Test Multiple Inheritance 1
generic_flying_car = FlyingCar("red", 1000, 200, "SUV", 30, seats=5)
generic_flying_car.move(100)
print(generic_flying_car.seats, generic_flying_car.wingspan,
generic_flying_car.form_factor)

# Test Multiple Inheritance 2
generic_flying_car_2 = FlyingCar(colour="red", weight=1000, max_speed=200,
form_factor="SUV", wingspan=30, seats=5)
generic_flying_car_2.move(100)

# Polymorphism
class Animal:
# we omit the __init__ method for brevity
    def move(self, speed):
        print(f"The animal is moving at a speed of {speed}")

generic_animal = Animal()
generic_animal.move(20)       

for movable_object in [generic_vehicle, electric_car_t2, generic_flying_car, generic_animal]:
    movable_object.move(20)