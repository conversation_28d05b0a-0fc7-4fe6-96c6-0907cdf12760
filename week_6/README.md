# Week 6 - Data Persistence and Property Decorators

## Overview
This week focuses on data persistence using the DAO (Data Access Object) pattern, @property decorators for computed attributes, and UML class diagrams for system design.

## Project Structure

### 1. Debugging Lab (`lab_week_6_debugging.py`)
Interactive debugging exercise with intentional errors for students to identify and fix.

**Intentional Bugs for Learning:**
- Type hint errors (speed: str instead of int)
- Logic errors (division by zero potential)
- Missing error handling for edge cases

### 2. Enhanced ToDoApp with Persistence (`ToDoAppWeek6/`)
Practical implementation of data persistence concepts.

**Files:**
- `main.py` - Enhanced application with DAO pattern integration
- `task.py` - Task classes with persistence preparation
- `tasklist.py` - TaskList with @property decorators
- `task_test_dao.py` - In-memory DAO for testing
- `task_csv_dao.py` - CSV file persistence implementation
- `task_pickle_dao.py` - Binary persistence implementation

## Key Concepts Demonstrated

### @Property Decorators
**Purpose**: Create computed attributes that behave like properties but execute code when accessed.

```python
@property
def uncompleted_tasks(self) -> list[Task]:
    """Property to get all uncompleted tasks - demonstrates @property decorator."""
    return [task for task in self.tasks if not task.completed]
```

**Benefits:**
- Attribute-like access: `task_list.uncompleted_tasks`
- Dynamic computation: Always returns current state
- Encapsulation: Hides implementation details

### DAO Pattern Implementation
**Purpose**: Abstract data storage implementation from business logic.

**Three DAO Implementations:**

1. **TaskTestDAO** - In-memory testing
   - Returns hardcoded test data
   - No file I/O dependencies
   - Perfect for development and testing

2. **TaskCsvDAO** - CSV file persistence
   - Human-readable storage format
   - Cross-platform compatibility
   - Easy data import/export

3. **TaskPickleDAO** - Binary persistence
   - Python object serialization
   - Efficient storage and retrieval
   - Maintains object relationships

### UML Class Diagrams
Week 6 introduces UML class diagram concepts showing:
- Class relationships and dependencies
- DAO pattern structure
- Interface consistency across implementations

## Data Persistence Flow

### Saving Tasks (CSV Example)
```python
def save_all_tasks(self, tasks: list[Task]) -> None:
    """Save all tasks to CSV file using DictWriter."""
    fieldnames = ["title", "type", "date_due", "completed", "interval", "completed_dates", "date_created"]
    
    with open(self.storage_path, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        
        for task in tasks:
            # Convert task object to dictionary for CSV storage
            row = {"title": task.title}
            # Handle different task types...
```

### Loading Tasks (CSV Example)
```python
def get_all_tasks(self) -> list[Task]:
    """Load all tasks from CSV file using DictReader."""
    with open(self.storage_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row in reader:
            # Convert CSV row back to task objects
            if row["type"] == "RecurringTask":
                task = RecurringTask(row["title"], date_due, interval)
            else:
                task = Task(row["title"], date_due)
```

## Running the Applications

### Debugging Lab
```bash
python lab_week_6_debugging.py
```

### Enhanced ToDoApp with Persistence
```bash
cd ToDoAppWeek6
python main.py
```

## Learning Progression

1. **Property Decorators** - Computed attributes with @property
2. **DAO Pattern** - Data access abstraction
3. **File I/O Operations** - CSV reading and writing
4. **Data Serialization** - Converting objects to/from storage formats
5. **Error Handling** - File operation exception management
6. **UML Diagrams** - System design visualization

## Week 6 Requirements Met

Based on the course memories, Week 6 specifically requires:

### ✅ @Property Decorators
- **uncompleted_tasks property** for computed attributes
- Demonstrates attribute-like access to dynamic data
- Encapsulates filtering logic within the property

### ✅ DAO Pattern Implementation
- **TaskTestDAO** for in-memory testing
- **TaskCsvDAO** for CSV file persistence
- Consistent interface across different storage methods

### ✅ UML Class Diagrams
- **TaskTestDAO and TaskCsvDAO classes** shown in system design
- Demonstrates data access layer architecture
- Shows separation of concerns between business logic and data storage

## Assessment Integration

This week prepares students for:
- Understanding data persistence concepts
- Implementing design patterns (DAO)
- Using Python decorators effectively
- Managing file I/O operations
- Creating maintainable, modular code architecture

---

**Note**: Week 6 builds upon previous OOP concepts while introducing crucial data persistence and system design principles essential for real-world application development.
