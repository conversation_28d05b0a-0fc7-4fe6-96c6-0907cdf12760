# Week 7 - SOLID Principles and Design Patterns

## Overview
This week focuses on SOLID principles, exception handling with try-except blocks, separation of concerns using CommandLineUI and TaskManagerController classes, and Factory pattern implementation for task creation.

## Project Structure

### Enhanced ToDoApp with SOLID Principles (`ToDoApp_Week7/`)
Professional implementation demonstrating advanced software design principles.

**Files:**
- `main.py` - Application entry point with dependency injection
- `ui.py` - CommandLineUI class for presentation layer
- `task_manager_controller.py` - Business logic controller
- `task_factory.py` - Factory pattern for task creation
- `task.py` - Enhanced Task classes
- `tasklist.py` - TaskList with DRY principle implementation
- `task_test_dao.py` - Test DAO implementation
- `task_csv_dao.py` - CSV persistence implementation
- `task_pickle_dao.py` - Binary persistence implementation

## Key Concepts Demonstrated

### SOLID Principles

#### **S - Single Responsibility Principle**
Each class has one reason to change:
- **CommandLineUI**: Only handles user interface concerns
- **TaskManagerController**: Only handles business logic
- **TaskFactory**: Only handles task creation
- **TaskList**: Only handles task collection management

#### **O - Open/Closed Principle**
Classes are open for extension, closed for modification:
- **TaskFactory**: New task types can be added without modifying existing code
- **DAO Pattern**: New storage methods can be added without changing business logic

#### **L - Liskov Substitution Principle**
Derived classes can replace base classes:
- **Task/RecurringTask**: RecurringTask can be used wherever Task is expected
- **DAO Implementations**: Any DAO can replace another without breaking functionality

#### **I - Interface Segregation Principle**
Clients depend only on interfaces they use:
- **Controller Methods**: Specific methods for specific operations
- **DAO Interface**: Consistent interface across different storage implementations

#### **D - Dependency Inversion Principle**
High-level modules don't depend on low-level modules:
- **UI depends on Controller**: Not on specific business logic implementations
- **Controller depends on DAO abstraction**: Not on specific storage implementations

### Separation of Concerns

#### **Presentation Layer (UI)**
```python
class CommandLineUI:
    """Command-line UI demonstrating separation of concerns."""
    
    def __init__(self) -> None:
        self.controller: TaskManagerController = None
    
    def display_menu(self) -> None:
        """Display main menu - UI responsibility only."""
        print("\nTo-Do List Manager - Week 7 SOLID Principles")
        print("1. Add a task")
        print("2. View tasks")
        # ... menu options
```

#### **Business Logic Layer (Controller)**
```python
class TaskManagerController:
    """Controller demonstrating SOLID principles and separation of concerns."""
    
    def create_task(self, title: str, due_date: datetime.datetime, 
                   is_recurring: bool = False, interval_days: int = 7) -> bool:
        """Create task using Factory pattern with exception handling."""
        try:
            if is_recurring:
                interval = datetime.timedelta(days=interval_days)
                task = TaskFactory.create_task(title, due_date, interval=interval)
            else:
                task = TaskFactory.create_task(title, due_date)
            
            self.task_list.add_task(task)
            return True
        except Exception as e:
            print(f"Error creating task: {e}")
            return False
```

### Factory Pattern Implementation

```python
class TaskFactory:
    """Factory class implementing Factory design pattern for task creation."""
    
    @staticmethod
    def create_task(title: str, date: datetime.datetime, **kwargs: Any) -> Task:
        """Create Task or RecurringTask based on parameters."""
        if "interval" in kwargs:
            interval = kwargs["interval"]
            return RecurringTask(title, date, interval)
        else:
            return Task(title, date)
```

**Benefits:**
- **Centralized Creation Logic**: All task creation in one place
- **Easy Extension**: New task types can be added easily
- **Open/Closed Principle**: Extend without modifying existing code

### Exception Handling with Try-Except Blocks

```python
def mark_task_completed(self, task_index: int) -> tuple[bool, str]:
    """Mark task as completed with comprehensive exception handling."""
    try:
        index = task_index - 1
        
        if not self.task_list.check_task_index(index):
            return False, "Invalid task number. Please try again."
        
        task = self.task_list.get_task(index)
        task.mark_as_completed()
        
        return True, f"Task '{task.title}' marked as completed."
        
    except Exception as e:
        return False, f"Error marking task as completed: {e}"
```

### DRY Principle Implementation

```python
def check_task_index(self, ix: int) -> bool:
    """Check if task index is valid - demonstrates DRY principle."""
    return 0 <= ix < len(self.tasks)

def get_task(self, index: int) -> Task:
    """Get task using DRY principle validation."""
    if self.check_task_index(index):
        return self.tasks[index]
    else:
        raise IndexError("Task index out of range")
```

## Running the Application

```bash
cd ToDoApp_Week7
python main.py
```

## Learning Progression

1. **SOLID Principles** - Professional software design principles
2. **Separation of Concerns** - UI, Business Logic, Data Access layers
3. **Factory Pattern** - Centralized object creation
4. **Exception Handling** - Comprehensive try-except blocks
5. **DRY Principle** - Don't Repeat Yourself implementation
6. **Dependency Inversion** - Depend on abstractions, not concretions

## Week 7 Requirements Met

Based on the course memories, Week 7 specifically requires:

### ✅ SOLID Principles Implementation
- **Single Responsibility**: Each class has one clear purpose
- **Open/Closed**: Factory pattern allows extension without modification
- **Liskov Substitution**: Task inheritance hierarchy
- **Interface Segregation**: Specific method interfaces
- **Dependency Inversion**: Controller depends on abstractions

### ✅ Exception Handling
- **Try-Except Blocks**: Comprehensive error handling throughout
- **Graceful Error Recovery**: Methods return success/failure status
- **User-Friendly Messages**: Clear error communication

### ✅ Separation of Concerns
- **CommandLineUI**: Presentation layer only
- **TaskManagerController**: Business logic layer only
- **DAO Classes**: Data access layer only

### ✅ Factory Pattern
- **TaskFactory**: Centralized task creation
- **Extensible Design**: Easy to add new task types
- **Parameter-Based Creation**: Uses **kwargs for flexibility

## Assessment Integration

This week demonstrates:
- Professional software architecture principles
- Industry-standard design patterns
- Robust error handling strategies
- Clean code organization and maintainability
- Scalable application design

---

**Note**: Week 7 represents the culmination of OOP learning, demonstrating how to build professional, maintainable, and extensible software systems using established design principles and patterns.
