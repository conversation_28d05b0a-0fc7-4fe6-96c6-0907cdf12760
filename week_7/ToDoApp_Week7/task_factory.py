"""
Task Factory Module - Week 7 SOLID Principles

This module implements the Factory design pattern for creating Task objects.
It demonstrates the Open/Closed Principle by allowing extension of task creation
without modifying existing code.

The Factory pattern provides:
- Centralized object creation logic
- Easy extension for new task types
- Abstraction of object instantiation details
- Consistent interface for task creation

Classes:
- TaskFactory: Factory class for creating Task and RecurringTask objects

Author: [VICTOR USMAN]
"""


# IMPORTS


import datetime  # For date/time operations
from typing import Any  # For type hints with **kwargs
from task import Task, RecurringTask  # Import Task classes


# TASK FACTORY CLASS DEFINITION


class TaskFactory:
    """
    Factory class for creating Task objects following the Factory design pattern.
    
    This class demonstrates:
    - Factory design pattern implementation
    - Open/Closed Principle (open for extension, closed for modification)
    - Single Responsibility Principle (only responsible for task creation)
    - Centralized object creation logic
    
    The factory determines the type of task to create based on the parameters
    provided, specifically the presence of an 'interval' parameter.
    """
    
    @staticmethod
    def create_task(title: str, date: datetime.datetime, **kwargs: Any) -> Task:
        """
        Create a Task or RecurringTask based on provided parameters.
        
        This static method implements the Factory pattern by creating the
        appropriate task type based on the presence of specific parameters.
        It follows the Open/Closed Principle - new task types can be added
        without modifying this method.
        
        Args:
            title (str): The title of the task
            date (datetime.datetime): The due date of the task
            **kwargs (Any): Additional parameters that determine task type
                - interval (datetime.timedelta): If present, creates RecurringTask
                - description (str): Optional task description
                
        Returns:
            Task: Either a Task or RecurringTask instance based on parameters
            
        Examples:
            >>> # Create a regular task
            >>> normal_task = TaskFactory.create_task(
            ...     "Do homework", 
            ...     datetime.datetime.now() + datetime.timedelta(days=3)
            ... )
            
            >>> # Create a recurring task
            >>> recurring_task = TaskFactory.create_task(
            ...     "Go to the gym", 
            ...     datetime.datetime.now(),
            ...     interval=datetime.timedelta(days=7)
            ... )
        """
        # Check if interval parameter is provided to determine task type
        if "interval" in kwargs:
            # Create RecurringTask with interval
            interval = kwargs["interval"]
            return RecurringTask(title, date, interval)
        else:
            # Create regular Task
            return Task(title, date)
    
    @staticmethod
    def create_task_with_description(title: str, date: datetime.datetime, 
                                   description: str = "", **kwargs: Any) -> Task:
        """
        Create a Task with description support (portfolio extension).
        
        This method extends the factory to support task descriptions,
        demonstrating how the Factory pattern can be extended without
        modifying existing code (Open/Closed Principle).
        
        Args:
            title (str): The title of the task
            date (datetime.datetime): The due date of the task
            description (str): The task description
            **kwargs (Any): Additional parameters for task creation
            
        Returns:
            Task: Task instance with description set
        """
        # Create task using main factory method
        task = TaskFactory.create_task(title, date, **kwargs)
        
        # Set description if the task supports it
        if hasattr(task, 'description'):
            task.description = description
            
        return task
    
    @staticmethod
    def get_supported_task_types() -> list[str]:
        """
        Get list of supported task types.
        
        This method provides information about available task types,
        useful for UI components and validation.
        
        Returns:
            list[str]: List of supported task type names
        """
        return ["Task", "RecurringTask"]
    
    @staticmethod
    def is_recurring_task_params(kwargs: dict[str, Any]) -> bool:
        """
        Check if parameters indicate a recurring task should be created.
        
        This helper method encapsulates the logic for determining task type,
        making it easier to extend or modify the criteria in the future.
        
        Args:
            kwargs (dict[str, Any]): Parameters to check
            
        Returns:
            bool: True if parameters indicate recurring task, False otherwise
        """
        return "interval" in kwargs and kwargs["interval"] is not None
