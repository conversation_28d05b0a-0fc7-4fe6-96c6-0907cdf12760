# Week 8 - Data Structures and Abstract Classes

## Overview
This week focuses on Python data structures (tuples, sets, dictionaries, nested lists) and abstract classes using ABC module with @abstractmethod decorator for creating blueprints and ensuring consistent interfaces across subclasses.

## Project Structure

### 1. Main Lab (`lab_week_8.py`)
Comprehensive demonstration of data structures and abstract classes.

**Key Exercises:**
- **Tuple Operations**: Variable swapping and packing/unpacking
- **Set Operations**: Intersection, union, difference operations
- **Dictionary Operations**: Histogram creation and frequency counting
- **Abstract Classes**: Dice implementation with @abstractmethod decorator

### 2. Abstract Classes in ToDo App (`todo_abstract_classes.py`)
Practical application of abstract classes in a real project context.

**Abstract Classes:**
- **AbstractTask**: Base class for all task types
- **AbstractDAO**: Base class for all data access objects
- **Concrete Implementations**: Task, RecurringTask, TaskTestDAO, TaskMemoryDAO

### 3. Test File (`test.py`)
Testing framework for validating implementations.

## Key Concepts Demonstrated

### Python Data Structures

#### **Tuples - Immutable Sequences**
```python
def tuple_swap(a: Any, b: Any) -> Tuple[Any, Any]:
    """Swap two variables using tuple packing/unpacking."""
    print(f"Before swap: a = {a}, b = {b}")
    a, b = b, a  # Tuple unpacking for swap
    print(f"After swap: a = {a}, b = {b}")
    return a, b
```

**Key Features:**
- **Immutable**: Cannot be changed after creation
- **Ordered**: Elements have a defined order
- **Packing/Unpacking**: Elegant variable operations
- **Multiple Assignment**: `x, y, z = 1, 2, 3`

#### **Sets - Unique Element Collections**
```python
def common_names(set1: Set[str], set2: Set[str]) -> Set[str]:
    """Find names that appear in both sets using intersection."""
    return set1 & set2  # Set intersection operator

# Set operations
set1 = {"Tom", "Jerry", "Hewey", "Dewey", "Louie"}
set2 = {"Tom", "Garfield", "Snoopy", "Hewey", "Dewey"}

print(f"Intersection: {set1 & set2}")  # Common elements
print(f"Union: {set1 | set2}")         # All unique elements
print(f"Difference: {set1 - set2}")    # Only in set1
```

**Key Features:**
- **Unique Elements**: No duplicates allowed
- **Unordered**: No defined sequence
- **Set Operations**: Intersection (&), Union (|), Difference (-)
- **Membership Testing**: Fast `in` operations

#### **Dictionaries - Key-Value Pairs**
```python
def histogram(lst: List[Any]) -> Dict[Any, int]:
    """Create histogram dictionary counting frequency of each element."""
    result = {}
    for item in lst:
        result[item] = result.get(item, 0) + 1
    return result

# Usage example
data = [1, 2, 3, 1, 2, 3, 4]
freq = histogram(data)  # {1: 2, 2: 2, 3: 2, 4: 1}
```

**Key Features:**
- **Key-Value Mapping**: Associate keys with values
- **Mutable**: Can be modified after creation
- **Fast Lookup**: O(1) average case for key access
- **Flexible Keys**: Any immutable type can be a key

### Abstract Classes with ABC Module

#### **Abstract Base Class Definition**
```python
from abc import ABC, abstractmethod

class Dice(ABC):
    """Abstract base class for dice objects demonstrating @abstractmethod decorator."""

    def __init__(self) -> None:
        """Initialize dice with no face value."""
        self.face: int = None

    @abstractmethod
    def roll(self) -> int:
        """Abstract method to roll dice - must be implemented by subclasses."""
        pass

    def get_face(self) -> int:
        """Get current face value."""
        return self.face
```

#### **Concrete Implementations**
```python
class SixSidedDice(Dice):
    """Six-sided dice implementation of abstract Dice class."""

    def roll(self) -> int:
        """Roll six-sided dice returning 1-6."""
        self.face = random.randint(1, 6)
        return self.face

class TenSidedDice(Dice):
    """Ten-sided dice implementation demonstrating polymorphism."""

    def roll(self) -> int:
        """Roll ten-sided dice returning 1-10."""
        self.face = random.randint(1, 10)
        return self.face
```

**Key Benefits:**
- **Interface Enforcement**: Subclasses must implement abstract methods
- **Consistent Interface**: All implementations follow same pattern
- **Polymorphism**: Same method names, different behaviors
- **Blueprint Pattern**: Define structure without implementation

### Nested Data Structures

#### **Complex Data Organization**
```python
# Nested lists for matrix operations
matrix = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]

# Dictionary of lists for grouped data
student_grades = {
    "Alice": [85, 92, 78],
    "Bob": [90, 88, 95],
    "Charlie": [76, 82, 89]
}

# List of dictionaries for records
tasks = [
    {"title": "Buy groceries", "priority": "high", "completed": False},
    {"title": "Walk dog", "priority": "medium", "completed": True}
]
```

## Running the Applications

### Main Lab Exercises
```bash
python lab_week_8.py
```

### Abstract Classes Demo
```bash
python todo_abstract_classes.py
```

### Run Tests
```bash
python test.py
```

## Learning Progression

1. **Tuples** - Immutable sequences for structured data
2. **Sets** - Unique collections for mathematical operations
3. **Dictionaries** - Key-value mappings for data organization
4. **Nested Structures** - Complex data organization patterns
5. **Abstract Classes** - Blueprint classes with enforced interfaces
6. **Polymorphism** - Same interface, different implementations

## Week 8 Requirements Met

Based on the course memories, Week 8 specifically requires:

### ✅ Python Data Structures
- **Tuples**: Variable swapping and packing/unpacking operations
- **Sets**: Intersection operations for finding common elements
- **Dictionaries**: Histogram creation for frequency counting
- **Nested Lists**: Complex data structure manipulation

### ✅ Abstract Classes with ABC Module
- **@abstractmethod Decorator**: Enforced method implementation
- **Abstract Base Classes**: Blueprint classes for consistent interfaces
- **Concrete Implementations**: Multiple classes implementing abstract methods
- **Polymorphism**: Same method names with different behaviors

### ✅ Practical Applications
- **Dice Implementation**: Abstract Dice class with concrete SixSidedDice and TenSidedDice
- **ToDo App Integration**: AbstractTask and AbstractDAO classes
- **Real-World Examples**: Histogram function, set operations, tuple swapping

## Assessment Integration

This week demonstrates:
- Advanced Python data structure usage
- Professional abstract class design patterns
- Polymorphism through consistent interfaces
- Complex data organization and manipulation
- Industry-standard blueprint patterns

---

**Note**: Week 8 completes the core OOP curriculum by introducing advanced data structures and abstract class patterns essential for building robust, maintainable software systems with consistent interfaces and polymorphic behavior.
